#!/usr/bin/env python3
"""
PIN-Generator GUI
Benutzerfreundliche Oberfläche für zeitbasierte PIN-Generierung und -Validierung
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import os
import binascii
from pin_generator_final import TimedPinGenerator, TimedPinValidator, generate_device_key


class PinGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Zeitbasiertes PIN-System")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Variablen
        self.device_key = None
        self.generator = None
        self.validator = None
        
        self.setup_ui()
        self.load_default_key()
        
    def setup_ui(self):
        """Erstellt die Benutzeroberfläche"""
        
        # Hauptframe mit Scrollbar
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # Titel
        title_label = ttk.Label(main_frame, text="Zeitbasiertes PIN-System", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # Device Key Sektion
        key_frame = ttk.LabelFrame(main_frame, text="Device Key (32 Bytes)", padding="10")
        key_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        key_frame.columnconfigure(1, weight=1)
        row += 1
        
        ttk.Label(key_frame, text="Hex Key:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.key_var = tk.StringVar()
        self.key_entry = ttk.Entry(key_frame, textvariable=self.key_var, width=70)
        self.key_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        key_buttons_frame = ttk.Frame(key_frame)
        key_buttons_frame.grid(row=0, column=2, sticky=tk.E)
        
        ttk.Button(key_buttons_frame, text="Generieren", 
                  command=self.generate_new_key).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(key_buttons_frame, text="Laden", 
                  command=self.load_key_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(key_buttons_frame, text="Speichern", 
                  command=self.save_key_file).pack(side=tk.LEFT)
        
        # Key Info
        self.key_info_var = tk.StringVar(value="Kein Key geladen")
        ttk.Label(key_frame, textvariable=self.key_info_var, 
                 foreground="gray").grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # PIN Generierung Sektion
        gen_frame = ttk.LabelFrame(main_frame, text="PIN Generierung", padding="10")
        gen_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        gen_frame.columnconfigure(1, weight=1)
        row += 1
        
        # Kaufdatum
        ttk.Label(gen_frame, text="Kaufdatum:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        date_frame = ttk.Frame(gen_frame)
        date_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        
        self.day_var = tk.StringVar(value=str(datetime.now().day))
        self.month_var = tk.StringVar(value=str(datetime.now().month))
        self.year_var = tk.StringVar(value=str(datetime.now().year))
        
        ttk.Entry(date_frame, textvariable=self.day_var, width=3).pack(side=tk.LEFT)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.month_var, width=3).pack(side=tk.LEFT)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.year_var, width=5).pack(side=tk.LEFT)
        
        ttk.Button(date_frame, text="Heute", 
                  command=self.set_today).pack(side=tk.LEFT, padx=(10, 0))
        
        # Gültigkeitsmonate
        ttk.Label(gen_frame, text="Gültigkeit (Monate):").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=2)
        validity_frame = ttk.Frame(gen_frame)
        validity_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        
        self.validity_var = tk.StringVar(value="12")
        validity_entry = ttk.Entry(validity_frame, textvariable=self.validity_var, width=10)
        validity_entry.pack(side=tk.LEFT)
        
        # Schnellauswahl Buttons
        quick_frame = ttk.Frame(validity_frame)
        quick_frame.pack(side=tk.LEFT, padx=(10, 0))
        
        for months in [1, 3, 6, 12, 24, 36]:
            ttk.Button(quick_frame, text=f"{months}M", width=4,
                      command=lambda m=months: self.validity_var.set(str(m))).pack(side=tk.LEFT, padx=1)
        
        ttk.Button(quick_frame, text="∞", width=4,
                  command=self.set_unlimited).pack(side=tk.LEFT, padx=1)
        
        # Generieren Button und Ergebnis
        ttk.Button(gen_frame, text="PIN Generieren", 
                  command=self.generate_pin).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Generierter PIN
        result_frame = ttk.Frame(gen_frame)
        result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        result_frame.columnconfigure(1, weight=1)
        
        ttk.Label(result_frame, text="Generierter PIN:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.generated_pin_var = tk.StringVar()
        pin_entry = ttk.Entry(result_frame, textvariable=self.generated_pin_var, 
                             font=("Courier", 14, "bold"), state="readonly")
        pin_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(result_frame, text="Kopieren", 
                  command=self.copy_pin).grid(row=0, column=2)
        
        # PIN Validierung Sektion
        val_frame = ttk.LabelFrame(main_frame, text="PIN Validierung", padding="10")
        val_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        val_frame.columnconfigure(1, weight=1)
        row += 1
        
        # PIN Eingabe
        ttk.Label(val_frame, text="PIN:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        pin_input_frame = ttk.Frame(val_frame)
        pin_input_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        pin_input_frame.columnconfigure(0, weight=1)
        
        self.pin_input_var = tk.StringVar()
        self.pin_input_entry = ttk.Entry(pin_input_frame, textvariable=self.pin_input_var, 
                                        font=("Courier", 12))
        self.pin_input_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(pin_input_frame, text="Einfügen", 
                  command=self.paste_pin).grid(row=0, column=1)
        
        # Validierungsdatum
        ttk.Label(val_frame, text="Prüfdatum:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=2)
        val_date_frame = ttk.Frame(val_frame)
        val_date_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        
        self.val_day_var = tk.StringVar(value=str(datetime.now().day))
        self.val_month_var = tk.StringVar(value=str(datetime.now().month))
        self.val_year_var = tk.StringVar(value=str(datetime.now().year))
        
        ttk.Entry(val_date_frame, textvariable=self.val_day_var, width=3).pack(side=tk.LEFT)
        ttk.Label(val_date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(val_date_frame, textvariable=self.val_month_var, width=3).pack(side=tk.LEFT)
        ttk.Label(val_date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(val_date_frame, textvariable=self.val_year_var, width=5).pack(side=tk.LEFT)
        
        ttk.Button(val_date_frame, text="Heute", 
                  command=self.set_validation_today).pack(side=tk.LEFT, padx=(10, 0))
        
        # Validieren Button
        ttk.Button(val_frame, text="PIN Validieren", 
                  command=self.validate_pin).grid(row=2, column=0, columnspan=2, pady=10)
        
        # Validierungsergebnis
        self.result_text = tk.Text(val_frame, height=8, width=70, wrap=tk.WORD)
        self.result_text.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Scrollbar für Ergebnis
        scrollbar = ttk.Scrollbar(val_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        scrollbar.grid(row=3, column=2, sticky=(tk.N, tk.S))
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        # Status Bar
        self.status_var = tk.StringVar(value="Bereit")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Bind Enter key für PIN-Eingabe
        self.pin_input_entry.bind('<Return>', lambda e: self.validate_pin())

        # Zusätzliche Features
        features_frame = ttk.LabelFrame(main_frame, text="Zusätzliche Features", padding="10")
        features_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        row += 1

        # Batch-Generierung
        ttk.Button(features_frame, text="Batch PIN-Generierung",
                  command=self.open_batch_window).pack(side=tk.LEFT, padx=(0, 10))

        # Key von Hex eingeben
        ttk.Button(features_frame, text="Key von Hex eingeben",
                  command=self.input_hex_key).pack(side=tk.LEFT, padx=(0, 10))

        # Über Dialog
        ttk.Button(features_frame, text="Über",
                  command=self.show_about).pack(side=tk.RIGHT)
        
    def load_default_key(self):
        """Lädt einen Standard-Key oder generiert einen neuen"""
        try:
            # Versuche gespeicherten Key zu laden
            if os.path.exists("default_device_key.bin"):
                with open("default_device_key.bin", "rb") as f:
                    key_data = f.read()
                    if len(key_data) == 32:
                        self.set_device_key(key_data)
                        self.status_var.set("Standard Device-Key geladen")
                        return
            
            # Generiere neuen Key
            self.generate_new_key()
            
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden des Standard-Keys: {e}")
            
    def set_device_key(self, key_bytes):
        """Setzt den Device-Key und aktualisiert die GUI"""
        try:
            if len(key_bytes) != 32:
                raise ValueError("Device-Key muss genau 32 Bytes lang sein")
                
            self.device_key = key_bytes
            self.generator = TimedPinGenerator(key_bytes)
            self.validator = TimedPinValidator(key_bytes)
            
            # Zeige Key in Hex-Format
            hex_key = binascii.hexlify(key_bytes).decode('ascii').upper()
            self.key_var.set(hex_key)
            
            # Update Info
            self.key_info_var.set(f"32-Byte Key geladen (CRC: {sum(key_bytes) % 65536:04X})")
            
        except Exception as e:
            messagebox.showerror("Fehler", f"Ungültiger Device-Key: {e}")
            
    def generate_new_key(self):
        """Generiert einen neuen Device-Key"""
        try:
            new_key = generate_device_key()
            self.set_device_key(new_key)
            
            # Speichere als Standard
            with open("default_device_key.bin", "wb") as f:
                f.write(new_key)
                
            self.status_var.set("Neuer Device-Key generiert und gespeichert")
            
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Generieren des Keys: {e}")
            
    def load_key_file(self):
        """Lädt Device-Key aus Datei"""
        try:
            filename = filedialog.askopenfilename(
                title="Device-Key laden",
                filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, "rb") as f:
                    key_data = f.read()
                    
                self.set_device_key(key_data)
                self.status_var.set(f"Device-Key aus {os.path.basename(filename)} geladen")
                
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden der Key-Datei: {e}")
            
    def save_key_file(self):
        """Speichert Device-Key in Datei"""
        try:
            if not self.device_key:
                messagebox.showwarning("Warnung", "Kein Device-Key zum Speichern vorhanden")
                return
                
            filename = filedialog.asksaveasfilename(
                title="Device-Key speichern",
                defaultextension=".bin",
                filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
            )
            
            if filename:
                with open(filename, "wb") as f:
                    f.write(self.device_key)
                    
                self.status_var.set(f"Device-Key in {os.path.basename(filename)} gespeichert")
                
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Speichern der Key-Datei: {e}")
            
    def set_today(self):
        """Setzt das Kaufdatum auf heute"""
        today = datetime.now()
        self.day_var.set(str(today.day))
        self.month_var.set(str(today.month))
        self.year_var.set(str(today.year))
        
    def set_validation_today(self):
        """Setzt das Validierungsdatum auf heute"""
        today = datetime.now()
        self.val_day_var.set(str(today.day))
        self.val_month_var.set(str(today.month))
        self.val_year_var.set(str(today.year))
        
    def set_unlimited(self):
        """Setzt unbegrenzte Gültigkeit"""
        self.validity_var.set("255")
        # Setze Datum auf 2099
        self.day_var.set("31")
        self.month_var.set("12")
        self.year_var.set("2099")
        
    def generate_pin(self):
        """Generiert einen PIN basierend auf den Eingaben"""
        try:
            if not self.generator:
                messagebox.showerror("Fehler", "Kein Device-Key geladen")
                return
                
            # Parse Datum
            day = int(self.day_var.get())
            month = int(self.month_var.get())
            year = int(self.year_var.get())
            purchase_date = datetime(year, month, day)
            
            # Parse Gültigkeit
            validity_months = int(self.validity_var.get())
            
            # Generiere PIN
            if validity_months == 255 and year == 2099:
                pin = self.generator.generate_unlimited_pin()
            else:
                pin = self.generator.generate_pin(purchase_date, validity_months)
                
            self.generated_pin_var.set(pin)
            self.status_var.set(f"PIN generiert für {purchase_date.strftime('%d.%m.%Y')}, {validity_months} Monate")
            
        except ValueError as e:
            messagebox.showerror("Eingabefehler", f"Ungültige Eingabe: {e}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Generieren des PINs: {e}")
            
    def copy_pin(self):
        """Kopiert den generierten PIN in die Zwischenablage"""
        pin = self.generated_pin_var.get()
        if pin:
            self.root.clipboard_clear()
            self.root.clipboard_append(pin)
            self.status_var.set("PIN in Zwischenablage kopiert")
        else:
            messagebox.showwarning("Warnung", "Kein PIN zum Kopieren vorhanden")
            
    def paste_pin(self):
        """Fügt PIN aus Zwischenablage ein"""
        try:
            clipboard_content = self.root.clipboard_get()
            # Nur Ziffern extrahieren
            pin = ''.join(filter(str.isdigit, clipboard_content))
            if len(pin) == 8:
                self.pin_input_var.set(pin)
                self.status_var.set("PIN aus Zwischenablage eingefügt")
            else:
                messagebox.showwarning("Warnung", "Zwischenablage enthält keinen gültigen 8-stelligen PIN")
        except tk.TclError:
            messagebox.showwarning("Warnung", "Zwischenablage ist leer")
            
    def validate_pin(self):
        """Validiert den eingegebenen PIN"""
        try:
            if not self.validator:
                messagebox.showerror("Fehler", "Kein Device-Key geladen")
                return
                
            pin = self.pin_input_var.get().strip()
            if not pin:
                messagebox.showwarning("Warnung", "Bitte geben Sie einen PIN ein")
                return
                
            # Parse Validierungsdatum
            day = int(self.val_day_var.get())
            month = int(self.val_month_var.get())
            year = int(self.val_year_var.get())
            validation_date = datetime(year, month, day)
            
            # Validiere PIN
            result = self.validator.validate_pin(pin, validation_date)
            
            # Zeige Ergebnis
            self.display_validation_result(result, validation_date)
            
        except ValueError as e:
            messagebox.showerror("Eingabefehler", f"Ungültige Eingabe: {e}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler bei der PIN-Validierung: {e}")
            
    def display_validation_result(self, result, validation_date):
        """Zeigt das Validierungsergebnis an"""
        self.result_text.delete(1.0, tk.END)
        
        # Header
        self.result_text.insert(tk.END, f"PIN-Validierung vom {validation_date.strftime('%d.%m.%Y')}\n")
        self.result_text.insert(tk.END, "=" * 50 + "\n\n")
        
        if result['valid']:
            if result['unlimited']:
                self.result_text.insert(tk.END, "✅ GÜLTIG - UNBEGRENZTE LIZENZ\n\n")
                self.result_text.insert(tk.END, f"Kaufdatum: {result['purchase_date'].strftime('%d.%m.%Y')}\n")
                self.result_text.insert(tk.END, "Ablaufdatum: Niemals\n")
                self.result_text.insert(tk.END, "Status: Unbegrenzt gültig\n")
            else:
                self.result_text.insert(tk.END, "✅ GÜLTIG - ZEITBASIERTE LIZENZ\n\n")
                self.result_text.insert(tk.END, f"Kaufdatum: {result['purchase_date'].strftime('%d.%m.%Y')}\n")
                self.result_text.insert(tk.END, f"Gültigkeitsdauer: {result['validity_months']} Monate\n")
                self.result_text.insert(tk.END, f"Ablaufdatum: {result['expires'].strftime('%d.%m.%Y')}\n")
                self.result_text.insert(tk.END, f"Verbleibende Tage: {result['days_remaining']}\n")
                
                if result['days_remaining'] <= 30:
                    self.result_text.insert(tk.END, "\n⚠️  WARNUNG: Lizenz läuft bald ab!\n")
        else:
            self.result_text.insert(tk.END, "❌ UNGÜLTIG\n\n")
            if 'error' in result:
                self.result_text.insert(tk.END, f"Fehler: {result['error']}\n")
            else:
                self.result_text.insert(tk.END, "Der PIN konnte nicht validiert werden oder ist abgelaufen.\n")
                
        self.status_var.set(f"PIN-Validierung abgeschlossen: {'GÜLTIG' if result['valid'] else 'UNGÜLTIG'}")


def main():
    """Hauptfunktion"""
    root = tk.Tk()
    app = PinGeneratorGUI(root)
    
    # Icon setzen (falls vorhanden)
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
        
    root.mainloop()


if __name__ == "__main__":
    main()
