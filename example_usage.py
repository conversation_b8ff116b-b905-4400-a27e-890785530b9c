#!/usr/bin/env python3
"""
Praktisches Beispiel für die Verwendung des zeitbasierten PIN-Systems
"""

import os
from datetime import datetime, timedelta
from pin_generator_final import TimedPinGenerator, TimedPinValidator, generate_device_key


class DeviceManager:
    """
    Beispiel-Implementierung eines Geräte-Managers
    """
    
    def __init__(self, device_id: str):
        self.device_id = device_id
        self.device_key_file = f"device_{device_id}_key.bin"
        self.device_key = self._load_or_create_device_key()
        self.validator = TimedPinValidator(self.device_key)
        
    def _load_or_create_device_key(self) -> bytes:
        """
        Lädt bestehenden Device-Key oder erstellt einen neuen
        """
        if os.path.exists(self.device_key_file):
            with open(self.device_key_file, 'rb') as f:
                key = f.read()
                if len(key) == 32:
                    print(f"Device-Key für Gerät {self.device_id} geladen")
                    return key
        
        # Neuen Key erstellen
        key = generate_device_key()
        with open(self.device_key_file, 'wb') as f:
            f.write(key)
        print(f"Neuer Device-Key für Gerät {self.device_id} erstellt")
        return key
    
    def check_license(self, pin: str) -> dict:
        """
        Prüft Lizenz-Status für gegebenen PIN
        """
        result = self.validator.validate_pin(pin)
        
        if result['valid']:
            if result['unlimited']:
                status = "UNLIMITED"
                message = "Unbegrenzte Lizenz aktiv"
            else:
                days_remaining = result['days_remaining']
                if days_remaining > 30:
                    status = "VALID"
                    message = f"Lizenz gültig für weitere {days_remaining} Tage"
                elif days_remaining > 0:
                    status = "EXPIRING"
                    message = f"Lizenz läuft in {days_remaining} Tagen ab!"
                else:
                    status = "EXPIRED"
                    message = "Lizenz ist abgelaufen"
        else:
            status = "INVALID"
            message = result.get('error', 'Ungültiger PIN')
            
        return {
            'status': status,
            'message': message,
            'details': result
        }


class LicenseGenerator:
    """
    Beispiel-Implementierung eines Lizenz-Generators (für Verkaufssystem)
    """
    
    def __init__(self):
        self.generators = {}  # Device-ID -> Generator
        
    def register_device(self, device_id: str, device_key: bytes):
        """
        Registriert ein Gerät für Lizenz-Generierung
        """
        self.generators[device_id] = TimedPinGenerator(device_key)
        print(f"Gerät {device_id} registriert")
        
    def generate_license_pin(self, device_id: str, validity_months: int) -> str:
        """
        Generiert Lizenz-PIN für spezifisches Gerät
        """
        if device_id not in self.generators:
            raise ValueError(f"Gerät {device_id} nicht registriert")
            
        generator = self.generators[device_id]
        purchase_date = datetime.now()
        pin = generator.generate_pin(purchase_date, validity_months)
        
        print(f"Lizenz-PIN für Gerät {device_id}: {pin}")
        print(f"Gültig für {validity_months} Monate ab {purchase_date.strftime('%d.%m.%Y')}")
        
        return pin
        
    def generate_unlimited_pin(self, device_id: str) -> str:
        """
        Generiert unbegrenzten PIN für interne Zwecke
        """
        if device_id not in self.generators:
            raise ValueError(f"Gerät {device_id} nicht registriert")
            
        generator = self.generators[device_id]
        pin = generator.generate_unlimited_pin()
        
        print(f"Unbegrenzter PIN für Gerät {device_id}: {pin}")
        
        return pin


def demo_customer_scenario():
    """
    Demonstriert ein typisches Kunden-Szenario
    """
    print("=== Kunden-Szenario Demo ===")
    
    # Gerät im Feld
    device = DeviceManager("DEVICE_001")
    
    # Kunde kauft 12-Monats-Lizenz
    print("\n1. Kunde kauft 12-Monats-Lizenz...")
    
    # Verkaufssystem generiert PIN
    license_gen = LicenseGenerator()
    license_gen.register_device("DEVICE_001", device.device_key)
    customer_pin = license_gen.generate_license_pin("DEVICE_001", 12)
    
    # Kunde gibt PIN am Gerät ein
    print(f"\n2. Kunde gibt PIN ein: {customer_pin}")
    license_status = device.check_license(customer_pin)
    print(f"Status: {license_status['message']}")
    
    # Simulation: 6 Monate später
    print("\n3. Simulation: 6 Monate später...")
    future_validator = TimedPinValidator(device.device_key)
    future_date = datetime.now() + timedelta(days=180)
    future_result = future_validator.validate_pin(customer_pin, future_date)
    
    if future_result['valid']:
        print(f"Lizenz noch gültig: {future_result['days_remaining']} Tage verbleibend")
    else:
        print("Lizenz abgelaufen")


def demo_internal_scenario():
    """
    Demonstriert internes Szenario mit unbegrenztem PIN
    """
    print("\n=== Internes Szenario Demo ===")
    
    # Internes Testgerät
    device = DeviceManager("INTERNAL_TEST")
    
    # Unbegrenzten PIN generieren
    license_gen = LicenseGenerator()
    license_gen.register_device("INTERNAL_TEST", device.device_key)
    internal_pin = license_gen.generate_unlimited_pin("INTERNAL_TEST")
    
    # PIN testen
    print(f"\nInterner PIN: {internal_pin}")
    license_status = device.check_license(internal_pin)
    print(f"Status: {license_status['message']}")
    
    # Test in ferner Zukunft
    print("\nTest in 10 Jahren...")
    future_validator = TimedPinValidator(device.device_key)
    future_date = datetime.now() + timedelta(days=3650)  # 10 Jahre
    future_result = future_validator.validate_pin(internal_pin, future_date)
    
    if future_result['valid'] and future_result['unlimited']:
        print("Unbegrenzte Lizenz funktioniert auch in 10 Jahren")
    else:
        print("Fehler bei unbegrenzter Lizenz")


def demo_security_features():
    """
    Demonstriert Sicherheitsfeatures
    """
    print("\n=== Sicherheits-Demo ===")
    
    # Zwei verschiedene Geräte
    device1 = DeviceManager("DEVICE_A")
    device2 = DeviceManager("DEVICE_B")
    
    # PIN für Gerät A generieren
    license_gen = LicenseGenerator()
    license_gen.register_device("DEVICE_A", device1.device_key)
    pin_for_device_a = license_gen.generate_license_pin("DEVICE_A", 6)
    
    print(f"\nPIN für Gerät A: {pin_for_device_a}")
    
    # PIN auf Gerät A testen (sollte funktionieren)
    result_a = device1.check_license(pin_for_device_a)
    print(f"Gerät A: {result_a['message']}")
    
    # Gleichen PIN auf Gerät B testen (sollte fehlschlagen)
    result_b = device2.check_license(pin_for_device_a)
    print(f"Gerät B: {result_b['message']}")
    
    # Ungültigen PIN testen
    print(f"\nTest mit ungültigem PIN...")
    invalid_result = device1.check_license("12345678")
    print(f"Ungültiger PIN: {invalid_result['message']}")


def demo_migration_scenario():
    """
    Demonstriert Migration von altem zu neuem System
    """
    print("\n=== Migrations-Szenario Demo ===")
    
    device = DeviceManager("LEGACY_DEVICE")
    
    # Simuliere alten PIN (könnte als unbegrenzt behandelt werden)
    old_pin = "87654321"
    
    print(f"Alter PIN: {old_pin}")
    result = device.check_license(old_pin)
    print(f"Alter PIN Status: {result['message']}")
    
    # Neuer zeitbasierter PIN
    license_gen = LicenseGenerator()
    license_gen.register_device("LEGACY_DEVICE", device.device_key)
    new_pin = license_gen.generate_license_pin("LEGACY_DEVICE", 24)
    
    print(f"\nNeuer zeitbasierter PIN: {new_pin}")
    new_result = device.check_license(new_pin)
    print(f"Neuer PIN Status: {new_result['message']}")


if __name__ == "__main__":
    print("Zeitbasiertes PIN-System - Anwendungsbeispiele")
    print("=" * 50)
    
    try:
        demo_customer_scenario()
        demo_internal_scenario()
        demo_security_features()
        demo_migration_scenario()
        
        print("\n" + "=" * 50)
        print("Demo abgeschlossen!")
        
    except Exception as e:
        print(f"Fehler während Demo: {e}")
        
    finally:
        # Aufräumen - Test-Dateien löschen
        test_files = [
            "device_DEVICE_001_key.bin",
            "device_INTERNAL_TEST_key.bin", 
            "device_DEVICE_A_key.bin",
            "device_DEVICE_B_key.bin",
            "device_LEGACY_DEVICE_key.bin"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                os.remove(file)
                print(f"Test-Datei {file} gelöscht")
