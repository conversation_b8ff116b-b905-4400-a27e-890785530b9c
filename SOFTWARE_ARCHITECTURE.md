# Software-Architektur: Zeitbasiertes PIN-System

## 1. Architektur-Übersicht

### 1.1 Systemkontext

```mermaid
graph TB
    User[<PERSON><PERSON><PERSON>] --> HMI[HMI/GUI]
    HMI --> PinSystem[PIN-System]
    PinSystem --> <PERSON>ceKey[Device-Key Storage]
    
    SalesSystem[Verkaufssystem] --> PinGenerator[PIN-Generator]
    PinGenerator --> DeviceDB[Device-Key Database]
    
    PinGenerator -.->|Generiert| PIN[8-stelliger PIN]
    PIN -.->|Eingabe| HMI
```

### 1.2 Systemarchitektur

Das PIN-System folgt einer modularen Architektur mit klarer Trennung von Verantwortlichkeiten:

```
┌─────────────────────────────────────────────────────────────┐
│                    PIN-System Architektur                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  PIN-Generator  │    │  PIN-Validator  │                │
│  │   (Server-Side) │    │ (Embedded-Side) │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│              Kryptographische Kernfunktionen               │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   HMAC-SHA256   │    │  Device-Key Mgmt│                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│                    Hardware Abstraction                    │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Secure Storage │    │   System Clock  │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 2. Komponentenarchitektur

### 2.1 Kernkomponenten

#### 2.1.1 PIN-Generator (Server-Side)
```
Klasse: OptimizedPinGenerator
Zweck: Generierung zeitbasierter PINs
Deployment: Verkaufssystem/Server
Abhängigkeiten: HMAC-SHA256, Device-Key Database

Methoden:
├── generate_pin(date, months, device_key) → pin
├── generate_unlimited_pin(device_key) → pin
└── validate_parameters(date, months) → boolean
```

#### 2.1.2 PIN-Validator (Embedded-Side)
```
Klasse: OptimizedPinValidator
Zweck: Validierung von PINs auf Embedded-Geräten
Deployment: Embedded-Gerät
Abhängigkeiten: HMAC-SHA256, Device-Key Storage

Methoden:
├── validate_pin(pin, current_date) → ValidationResult
├── _test_combination(pin, days, months) → boolean
└── _extract_license_info(days, months) → LicenseInfo
```

### 2.2 Datenstrukturen

#### 2.2.1 Eingabedaten-Format
```c
typedef struct {
    uint16_t days_since_ref;    // Tage seit 1.1.2020 (0-65535)
    uint8_t  validity_months;   // Gültigkeitsmonate (1-36, 255)
} __attribute__((packed)) license_input_t;
```

#### 2.2.2 Validierungsergebnis
```c
typedef struct {
    bool     valid;             // Gültigkeitsstatus
    bool     unlimited;         // Unbegrenzte Lizenz?
    uint16_t purchase_days;     // Kaufdatum (Tage seit Referenz)
    uint8_t  validity_months;   // Gültigkeitsmonate
    uint16_t expiry_days;       // Ablaufdatum (Tage seit Referenz)
    int16_t  days_remaining;    // Verbleibende Tage
} validation_result_t;
```

## 3. Deployment-Architektur

### 3.1 Server-Side Deployment (PIN-Generierung)

```
┌─────────────────────────────────────────────────────────────┐
│                    Verkaufssystem                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Web-Frontend  │    │   PIN-Service   │                │
│  │   (GUI/API)     │    │  (Generator)    │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Device-Key DB  │    │   Audit-Log     │                │
│  │   (PostgreSQL)  │    │   (Logging)     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Embedded-Side Deployment (PIN-Validierung)

```
┌─────────────────────────────────────────────────────────────┐
│                   Embedded-Gerät                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │      HMI        │    │  Application    │                │
│  │  (PIN-Eingabe)  │    │   (Features)    │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  PIN-Validator  │    │   System Clock  │                │
│  │   (Optimiert)   │    │    (RTC)        │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Device-Key     │    │  Secure Storage │                │
│  │  (32 Bytes)     │    │ (Flash/EEPROM)  │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 4. Datenfluss-Architektur

### 4.1 PIN-Generierung Datenfluss

```mermaid
sequenceDiagram
    participant Sales as Verkaufssystem
    participant DB as Device-Key DB
    participant Gen as PIN-Generator
    participant Customer as Kunde
    
    Sales->>DB: Lade Device-Key für Gerät
    DB-->>Sales: Device-Key (32 Bytes)
    Sales->>Gen: generate_pin(date, months, key)
    Gen->>Gen: Berechne HMAC-SHA256
    Gen->>Gen: Kombiniere mit XOR
    Gen->>Gen: Normalisiere auf 8 Stellen
    Gen-->>Sales: 8-stelliger PIN
    Sales->>Customer: PIN per E-Mail/Portal
```

### 4.2 PIN-Validierung Datenfluss

```mermaid
sequenceDiagram
    participant User as Benutzer
    participant HMI as HMI
    participant Val as PIN-Validator
    participant Storage as Secure Storage
    participant App as Application
    
    User->>HMI: Eingabe 8-stelliger PIN
    HMI->>Val: validate_pin(pin, current_date)
    Val->>Storage: Lade Device-Key
    Storage-->>Val: Device-Key (32 Bytes)
    Val->>Val: Teste unbegrenzte Lizenz
    Val->>Val: Brute-Force Suche (optimiert)
    Val->>Val: Berechne Lizenzinformationen
    Val-->>HMI: ValidationResult
    HMI->>App: Feature freischalten/sperren
    HMI->>User: Anzeige Lizenzstatus
```

## 5. Sicherheitsarchitektur

### 5.1 Kryptographische Architektur

```
┌─────────────────────────────────────────────────────────────┐
│                 Kryptographische Schichten                  │
├─────────────────────────────────────────────────────────────┤
│  Anwendungsschicht                                          │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  PIN-Generator  │    │  PIN-Validator  │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  Kryptographische Schicht                                   │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   HMAC-SHA256   │    │  Device-Binding │                │
│  │   (RFC 2104)    │    │   (32-Byte Key) │                │
│  └─────────────────┘    └─────────────────┘                │
├─────────────────────────────────────────────────────────────┤
│  Hardware-Schicht                                           │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Secure Element │    │  Hardware RNG   │                │
│  │   (Optional)    │    │  (Key-Gen)      │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Bedrohungsmodell und Schutzmaßnahmen

| Bedrohung | Schutzmaßnahme | Implementierung |
|-----------|----------------|-----------------|
| PIN-Sharing zwischen Geräten | Gerätespezifische Bindung | HMAC mit Device-Key |
| Reverse Engineering | Keine offensichtliche Struktur | XOR-Kombination mit HMAC |
| Brute-Force Angriffe | Begrenzte Gültigkeit | Zeitbasierte Ablaufprüfung |
| Device-Key Extraktion | Secure Storage | Hardware-Schutz (HSM/SE) |
| Replay-Angriffe | Zeitbasierte Validierung | Ablaufdatum-Prüfung |

## 6. Performance-Architektur

### 6.1 Embedded-Optimierungen

```
┌─────────────────────────────────────────────────────────────┐
│                Performance-Optimierungen                    │
├─────────────────────────────────────────────────────────────┤
│  Algorithmus-Ebene                                          │
│  • Begrenzte Suche: 36 statt 255 Monate                   │
│  • Frühe Terminierung bei unbegrenzten Lizenzen            │
│  • Optimierte Suchstrategie                                │
├─────────────────────────────────────────────────────────────┤
│  Implementierungs-Ebene                                     │
│  • Minimale Datenstrukturen                                │
│  • Stack-basierte Allokation                               │
│  • Optimierte HMAC-Implementierung                         │
├─────────────────────────────────────────────────────────────┤
│  Hardware-Ebene                                             │
│  • Hardware-AES (falls verfügbar)                          │
│  • Cache-optimierte Speicherzugriffe                       │
│  • Low-Power Modi zwischen Validierungen                   │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 Performance-Metriken

| Metrik | Zielwert | Gemessen | Status |
|--------|----------|----------|--------|
| Validierungszeit | ≤ 500ms | ~191ms | ✅ |
| Speicherverbrauch | ≤ 1KB | ~70B | ✅ |
| CPU-Zyklen | ≤ 50M | ~19M | ✅ |
| Flash-Verbrauch | ≤ 4KB | ~2KB | ✅ |

## 7. Schnittstellen-Architektur

### 7.1 API-Definitionen

#### 7.1.1 PIN-Generator API
```c
// PIN-Generierung
typedef enum {
    PIN_GEN_SUCCESS = 0,
    PIN_GEN_INVALID_DATE = -1,
    PIN_GEN_INVALID_MONTHS = -2,
    PIN_GEN_INVALID_KEY = -3
} pin_gen_result_t;

pin_gen_result_t generate_pin(
    const datetime_t* purchase_date,
    uint8_t validity_months,
    const uint8_t device_key[32],
    char pin_out[9]  // 8 Ziffern + Null-Terminator
);
```

#### 7.1.2 PIN-Validator API
```c
// PIN-Validierung
typedef enum {
    PIN_VAL_SUCCESS = 0,
    PIN_VAL_INVALID_FORMAT = -1,
    PIN_VAL_INVALID_KEY = -2,
    PIN_VAL_NOT_FOUND = -3
} pin_val_result_t;

pin_val_result_t validate_pin(
    const char pin[8],
    const datetime_t* current_date,
    const uint8_t device_key[32],
    validation_result_t* result_out
);
```

### 7.2 Konfigurationsschnittstelle

```c
// Konfigurationskonstanten
#define PIN_SYSTEM_MAX_VALIDITY_MONTHS  36
#define PIN_SYSTEM_UNLIMITED_MONTHS     255
#define PIN_SYSTEM_DEVICE_KEY_SIZE      32
#define PIN_SYSTEM_PIN_LENGTH           8
#define PIN_SYSTEM_REFERENCE_YEAR       2020

// Konfigurierbare Parameter
typedef struct {
    uint16_t max_search_days;       // Maximale Suchtage (Standard: 3650)
    uint8_t  max_validity_months;   // Maximale Gültigkeit (Standard: 36)
    bool     enable_unlimited;      // Unbegrenzte Lizenzen erlauben
    uint32_t timeout_ms;           // Validierungs-Timeout
} pin_system_config_t;
```

## 8. Fehlerbehandlungs-Architektur

### 8.1 Fehlerklassifikation

```
┌─────────────────────────────────────────────────────────────┐
│                    Fehlerbehandlung                         │
├─────────────────────────────────────────────────────────────┤
│  Kritische Fehler (System-Stop)                            │
│  • Device-Key nicht verfügbar                              │
│  • Kryptographische Bibliothek-Fehler                      │
│  • Hardware-Fehler (RTC, Storage)                          │
├─────────────────────────────────────────────────────────────┤
│  Funktionale Fehler (Graceful Degradation)                 │
│  • Ungültiger PIN                                          │
│  • Abgelaufene Lizenz                                      │
│  • Ungültige Eingabeparameter                              │
├─────────────────────────────────────────────────────────────┤
│  Performance-Fehler (Timeout)                              │
│  • Validierung dauert zu lange                             │
│  • Speicher-Erschöpfung                                    │
│  • CPU-Überlastung                                         │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Fehlerbehandlungsstrategien

| Fehlertyp | Strategie | Implementierung |
|-----------|-----------|-----------------|
| Ungültiger PIN | Benutzerinformation | Spezifische Fehlermeldung |
| Abgelaufene Lizenz | Graceful Degradation | Feature-Sperrung mit Info |
| Device-Key Fehler | System-Neustart | Watchdog-Reset |
| Timeout | Abbruch mit Retry | Maximale Versuche begrenzen |

## 9. Test-Architektur

### 9.1 Test-Pyramide

```
┌─────────────────────────────────────────────────────────────┐
│                      Test-Architektur                       │
├─────────────────────────────────────────────────────────────┤
│  End-to-End Tests                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Vollständige PIN-Generierung und -Validierung        │ │
│  │ • Hardware-in-the-Loop Tests                           │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Integrationstests                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Generator-Validator Kompatibilität                   │ │
│  │ • Kryptographische Korrektheit                         │ │
│  │ • Performance unter Last                               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  Unit Tests                                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Einzelne Funktionen                                  │ │
│  │ • Grenzwerte und Fehlerfälle                          │ │
│  │ • Kryptographische Primitive                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 9.2 Test-Infrastruktur

```c
// Test-Framework Integration
typedef struct {
    const char* test_name;
    bool (*test_function)(void);
    uint32_t expected_cycles;
    uint32_t max_memory_kb;
} test_case_t;

// Performance-Test Makros
#define PERFORMANCE_TEST(name, max_ms) \
    bool test_##name(void) { \
        uint32_t start = get_cycle_count(); \
        /* Test-Code */ \
        uint32_t cycles = get_cycle_count() - start; \
        return (cycles <= (max_ms * CPU_FREQ_MHZ * 1000)); \
    }
```

## 10. Wartungs- und Evolutions-Architektur

### 10.1 Versionierung

```
Version Schema: MAJOR.MINOR.PATCH
├── MAJOR: Inkompatible API-Änderungen
├── MINOR: Neue Features (rückwärtskompatibel)
└── PATCH: Bugfixes und Optimierungen

Beispiel: v2.1.3
├── v2.x.x: Optimierte Embedded-Version (36 Monate)
├── v2.1.x: GUI und erweiterte Features
└── v2.1.3: Performance-Optimierungen
```

### 10.2 Upgrade-Pfade

```mermaid
graph LR
    V1[v1.x Original] --> V2[v2.0 Optimiert]
    V2 --> V21[v2.1 + GUI]
    V21 --> V22[v2.2 + Features]
    
    V1 -.->|Legacy Support| V21
    V2 -.->|Embedded Only| V22
```

### 10.3 Konfigurationsmanagement

```c
// Compile-Time Konfiguration
#ifdef EMBEDDED_TARGET
    #define MAX_VALIDITY_MONTHS 36
    #define ENABLE_PERFORMANCE_OPTIMIZATIONS 1
#else
    #define MAX_VALIDITY_MONTHS 255
    #define ENABLE_FULL_FEATURES 1
#endif

// Runtime-Konfiguration
typedef struct {
    uint8_t version_major;
    uint8_t version_minor;
    uint8_t version_patch;
    uint32_t features_enabled;
    pin_system_config_t config;
} pin_system_info_t;
```

Diese Architektur gewährleistet eine saubere Trennung der Verantwortlichkeiten, optimale Performance für Embedded-Systeme und einfache Wartbarkeit für zukünftige Erweiterungen.
