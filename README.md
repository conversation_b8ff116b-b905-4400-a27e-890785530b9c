# Zeitbasiertes PIN-System

Ein sicheres System zur Generierung und Validierung von 8-stelligen PINs mit zeitbasierter Lizenzierung.

## Überblick

Dieses System ermöglicht es, über 8-stellige PINs zeitbasierte Lizenzen zu verwalten. Jeder PIN kodiert:
- Kaufdatum
- Gültigkeitsdauer in Monaten
- Gerätespezifische Validierung über CRC

## Sicherheitsfeatures

- **AES-128 Verschlüsselung** mit gerätespezifischem Schlüssel
- **CRC16-Validierung** verhindert Manipulation
- **Gerätespezifische Bindung** über 32-Byte Device-Key
- **Zeitbasierte Validierung** mit Ablaufdatum

## Installation

```bash
pip install cryptography
```

## Verwendung

### PIN-Generierung

```python
from pin_generator_final import TimedPinGenerator, generate_device_key
from datetime import datetime

# Gerätespezifischen Schlüssel generieren (einmalig pro Gerät)
device_key = generate_device_key()

# Generator initialisieren
generator = TimedPinGenerator(device_key)

# PIN für 12 Monate ab heute generieren
purchase_date = datetime.now()
pin = generator.generate_pin(purchase_date, 12)
print(f"PIN: {pin}")

# Unbegrenzten PIN für interne Zwecke
unlimited_pin = generator.generate_unlimited_pin()
print(f"Unbegrenzter PIN: {unlimited_pin}")
```

### PIN-Validierung

```python
from pin_generator_final import TimedPinValidator
from datetime import datetime

# Validator mit gleichem Device-Key initialisieren
validator = TimedPinValidator(device_key)

# PIN validieren
result = validator.validate_pin(pin)

if result['valid']:
    if result['unlimited']:
        print("Unbegrenzte Lizenz aktiv")
    else:
        print(f"Lizenz gültig bis: {result['expires']}")
        print(f"Verbleibende Tage: {result['days_remaining']}")
else:
    print(f"Ungültiger PIN: {result.get('error', 'Unbekannter Fehler')}")
```

## Datenformat

### PIN-Struktur (8 Bytes vor Verschlüsselung)

| Bytes | Beschreibung |
|-------|--------------|
| 0-3   | Kaufdatum (Tage seit 1.1.2020) |
| 4     | Gültigkeitsmonate (1-255) |
| 5     | Reserviert (0x00) |
| 6-7   | CRC16 des Device-Keys |

### Spezialwerte

- **Unbegrenzte Lizenz**: Kaufdatum = 31.12.2099, Monate = 255
- **Maximale Gültigkeit**: 254 Monate (21+ Jahre)

## Sicherheitsaspekte

### Schutz vor Reverse Engineering

1. **Gerätespezifische Bindung**: Jedes Gerät hat einen einzigartigen 32-Byte Schlüssel
2. **AES-Verschlüsselung**: Ohne Device-Key ist Entschlüsselung praktisch unmöglich
3. **CRC-Validierung**: Verhindert zufällige oder manipulierte PINs
4. **Kompakte Kodierung**: 8-stelliger PIN gibt keine offensichtlichen Hinweise auf Struktur

### Empfohlene Implementierung

1. **Device-Key Speicherung**: 
   - In sicherem Speicher (HSM, Secure Element)
   - Oder verschlüsselt mit Hardware-spezifischen Daten

2. **PIN-Generierung**:
   - Nur auf sicheren Systemen (nicht auf Endgeräten)
   - Mit Audit-Log für generierte PINs

3. **Validierung**:
   - Lokale Validierung auf Endgerät
   - Keine Netzwerkverbindung erforderlich

## Rückwärtskompatibilität

Das System ist so konzipiert, dass bestehende Geräte über Software-Updates erweitert werden können:

1. **Bestehende PINs**: Können als "unbegrenzt" behandelt werden
2. **Neue PINs**: Verwenden das zeitbasierte System
3. **HMI unverändert**: 8-stellige PIN-Eingabe bleibt gleich

## Beispiel-Integration

```python
class LicenseManager:
    def __init__(self, device_key_file: str):
        with open(device_key_file, 'rb') as f:
            self.device_key = f.read()
        self.validator = TimedPinValidator(self.device_key)
        
    def check_feature_access(self, pin: str) -> bool:
        """Prüft ob Feature-Zugriff erlaubt ist"""
        result = self.validator.validate_pin(pin)
        return result['valid']
        
    def get_license_info(self, pin: str) -> dict:
        """Gibt detaillierte Lizenzinformationen zurück"""
        return self.validator.validate_pin(pin)

# Verwendung in der Anwendung
license_mgr = LicenseManager('device_key.bin')

if license_mgr.check_feature_access(user_pin):
    # Feature freischalten
    enable_premium_features()
else:
    # Zugriff verweigern
    show_license_expired_message()
```

## Tests ausführen

```bash
python test_pin_system.py
```

## Hauptdateien

- `pin_generator_final.py` - Hauptimplementierung des PIN-Systems
- `test_pin_system.py` - Umfassende Test-Suite
- `example_usage.py` - Praktische Anwendungsbeispiele
- `README.md` - Diese Dokumentation

## Lizenz

Dieses System ist für interne Verwendung konzipiert. Alle Rechte vorbehalten.
