#!/usr/bin/env python3
"""
Optimiertes zeitbasiertes PIN-System für Embedded Systeme
Beschränkt auf 36 Monate Gültigkeit für maximale Performance bei 100MHz CPU
"""

import struct
import hashlib
import hmac
from datetime import datetime, timedelta
import os


class OptimizedPinGenerator:
    """
    Optimierter PIN-Generator für Embedded Systeme (max. 36 Monate)
    """
    
    # Konstanten für Embedded-Optimierung
    MAX_VALIDITY_MONTHS = 36
    UNLIMITED_MONTHS = 255  # Spezialwert für unbegrenzte Lizenz
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 32-Byte gerätespezifischer Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN für gegebenes Kaufdatum und Gültigkeitsdauer
        
        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer in Monaten (1-36 oder 255 für unbegrenzt)
            
        Returns:
            8-stelliger PIN als String
            
        Raises:
            ValueError: Bei ungültigen Parametern
        """
        # Validierung der Eingabeparameter
        if validity_months != self.UNLIMITED_MONTHS and (validity_months < 1 or validity_months > self.MAX_VALIDITY_MONTHS):
            raise ValueError(f"Gültigkeitsmonate müssen zwischen 1 und {self.MAX_VALIDITY_MONTHS} liegen (oder 255 für unbegrenzt)")
            
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 65535:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs (2020-2099)")
        
        # Erstelle Eingabedaten für HMAC (3 Bytes)
        input_data = struct.pack('<H B', days_since_ref, validity_months)
        
        # Berechne HMAC mit device_key
        mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
        
        # Verwende erste 4 Bytes des HMAC
        mac_int = struct.unpack('<I', mac[:4])[0]
        
        # Kombiniere mit Eingabedaten für eindeutige PIN
        combined = (mac_int ^ (days_since_ref << 16) ^ (validity_months << 8)) & 0xFFFFFFFF
        
        # Auf 8 Stellen normalisieren
        pin = str(combined % 100000000).zfill(8)
        
        return pin
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        
        Returns:
            8-stelliger PIN für unbegrenzte Nutzung
        """
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, self.UNLIMITED_MONTHS)


class OptimizedPinValidator:
    """
    Optimierter PIN-Validator für Embedded Systeme
    Beschränkt auf 36 Monate für maximale Performance
    """
    
    # Konstanten (müssen mit Generator übereinstimmen)
    MAX_VALIDITY_MONTHS = 36
    UNLIMITED_MONTHS = 255
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert einen PIN mit optimierter Suche für Embedded Systeme
        
        Args:
            pin: 8-stelliger PIN
            current_date: Aktuelles Datum (Standard: heute)
            
        Returns:
            Dictionary mit Validierungsergebnis und Lizenzinformationen
        """
        if current_date is None:
            current_date = datetime.now()
            
        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }
        
        pin_int = int(pin)
        ref_date = datetime(2020, 1, 1)
        
        # Optimierte Suche für Embedded Systeme
        
        # 1. Teste unbegrenzte Lizenz zuerst (1 Berechnung)
        special_days = (datetime(2099, 12, 31).date() - ref_date.date()).days
        if self._test_combination(pin_int, special_days, self.UNLIMITED_MONTHS):
            return {
                'valid': True,
                'unlimited': True,
                'purchase_date': datetime(2099, 12, 31),
                'validity_months': self.UNLIMITED_MONTHS,
                'expires': None
            }
        
        # 2. Optimierte Suche: Nur 2020-2030 und max. 36 Monate
        # Das reduziert die Suchzeit erheblich: 3650 Tage × 36 Monate = 131.400 Berechnungen
        # Bei 100MHz CPU: ~1-2 Sekunden statt 10+ Sekunden
        
        for days_since_ref in range(0, 3650, 1):  # 2020-2030 (10 Jahre)
            for validity_months in range(1, self.MAX_VALIDITY_MONTHS + 1):  # 1-36 Monate
                if self._test_combination(pin_int, days_since_ref, validity_months):
                    purchase_date = ref_date + timedelta(days=days_since_ref)
                    expiry_date = purchase_date + timedelta(days=validity_months * 30)
                    is_valid = current_date <= expiry_date
                    
                    return {
                        'valid': is_valid,
                        'unlimited': False,
                        'purchase_date': purchase_date,
                        'validity_months': validity_months,
                        'expires': expiry_date,
                        'days_remaining': (expiry_date - current_date).days if is_valid else 0
                    }
        
        return {
            'valid': False,
            'error': 'PIN konnte nicht validiert werden'
        }
    
    def _test_combination(self, target_pin: int, days: int, months: int) -> bool:
        """
        Testet eine spezifische Kombination von Tagen und Monaten
        Optimiert für minimale Berechnungen
        """
        try:
            # Erstelle Eingabedaten
            input_data = struct.pack('<H B', days, months)
            
            # Berechne HMAC
            mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
            mac_int = struct.unpack('<I', mac[:4])[0]
            
            # Kombiniere für PIN
            combined = (mac_int ^ (days << 16) ^ (months << 8)) & 0xFFFFFFFF
            calculated_pin = combined % 100000000
            
            return calculated_pin == target_pin
            
        except (struct.error, ValueError):
            return False


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 32-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(32)


# Performance-Benchmarking
def benchmark_validation(device_key: bytes, num_tests: int = 50):
    """
    Benchmarkt die optimierte Validierungsperformance
    """
    import time
    
    generator = OptimizedPinGenerator(device_key)
    validator = OptimizedPinValidator(device_key)
    
    # Generiere Test-PINs für verschiedene Gültigkeiten
    test_pins = []
    test_date = datetime(2024, 6, 15)
    
    for months in [1, 6, 12, 18, 24, 36]:
        pin = generator.generate_pin(test_date, months)
        test_pins.append((pin, months))
    
    # Füge unbegrenzten PIN hinzu
    unlimited_pin = generator.generate_unlimited_pin()
    test_pins.append((unlimited_pin, 255))
    
    # Benchmark
    print(f"Optimiertes PIN-System Benchmark (max. {OptimizedPinValidator.MAX_VALIDITY_MONTHS} Monate):")
    print("=" * 70)
    
    total_time = 0
    total_validations = 0
    
    for pin, months in test_pins:
        start_time = time.time()
        
        for _ in range(num_tests):
            result = validator.validate_pin(pin, test_date)
            assert result['valid'], f"PIN {pin} sollte gültig sein"
        
        end_time = time.time()
        
        test_time = (end_time - start_time) * 1000
        avg_time = test_time / num_tests
        
        total_time += test_time
        total_validations += num_tests
        
        validity_str = "Unbegrenzt" if months == 255 else f"{months} Monate"
        print(f"• {validity_str:12} - PIN: {pin} - {avg_time:.1f}ms pro Validierung")
    
    overall_avg = total_time / total_validations
    print(f"\nGesamtdurchschnitt: {overall_avg:.1f}ms pro Validierung")
    print(f"Geschätzte CPU-Zyklen bei 100MHz: ~{overall_avg * 100:.0f}k Zyklen")
    
    # Vergleich mit nicht-optimierter Version
    max_calculations = 3650 * 36  # Tage × Monate
    print(f"\nOptimierung:")
    print(f"• Maximale Berechnungen: {max_calculations:,}")
    print(f"• Reduzierung um ~86% gegenüber 255-Monate-Version")
    print(f"• Geeignet für 100MHz Embedded CPU")


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = OptimizedPinGenerator(device_key)
    validator = OptimizedPinValidator(device_key)
    
    print("Optimiertes PIN-System für Embedded Systeme")
    print("=" * 50)
    
    # PIN für 15 Monate ab heute generieren
    purchase_date = datetime.now()
    pin = generator.generate_pin(purchase_date, 15)
    print(f"Generierter PIN (15 Monate): {pin}")
    
    # PIN validieren
    result = validator.validate_pin(pin)
    print(f"Validierungsergebnis: {result}")
    
    # Test mit 36 Monaten (Maximum)
    max_pin = generator.generate_pin(purchase_date, 36)
    print(f"\nGenerierter PIN (36 Monate): {max_pin}")
    
    max_result = validator.validate_pin(max_pin)
    print(f"Validierungsergebnis: {max_result}")
    
    # Unbegrenzten PIN generieren
    unlimited_pin = generator.generate_unlimited_pin()
    print(f"\nUnbegrenzter PIN: {unlimited_pin}")
    
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzte Validierung: {unlimited_result}")
    
    # Performance-Test
    print("\n" + "="*70)
    benchmark_validation(device_key, 20)
