#!/usr/bin/env python3
"""
Embedded-optimiertes zeitbasiertes PIN-System
Optimiert für Low-Power Embedded Systeme mit begrenzten Ressourcen
"""

import struct
import hashlib
from datetime import datetime, timedelta
import os


class EmbeddedPinGenerator:
    """
    Embedded-optimierter PIN-Generator mit direkter Kodierung
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 16-Byte gerätespezifischer Schlüssel (reduziert von 32)
        """
        if len(device_key) != 16:
            raise ValueError("Device key muss genau 16 Bytes lang sein")
        self.device_key = device_key
        
    def _simple_hash(self, data: bytes) -> int:
        """
        Einfache aber sichere Hash-Funktion für Embedded Systeme
        Verwendet nur grundlegende Operationen
        """
        # Verwende ersten Teil des Device-Keys als Seed
        seed = struct.unpack('<I', self.device_key[:4])[0]
        
        hash_val = seed
        for i, byte in enumerate(data):
            # Einfache aber effektive Mixing-Funktion
            hash_val ^= byte << (i % 24)
            hash_val = ((hash_val << 13) | (hash_val >> 19)) & 0xFFFFFFFF
            hash_val ^= struct.unpack('<I', self.device_key[4 + (i % 3) * 4:8 + (i % 3) * 4])[0]
            
        return hash_val
        
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN mit direkter Kodierung
        
        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer in Monaten (1-255)
            
        Returns:
            8-stelliger PIN als String
        """
        if validity_months < 1 or validity_months > 255:
            raise ValueError("Gültigkeitsmonate müssen zwischen 1 und 255 liegen")
            
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 65535:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs")
        
        # Kompakte Kodierung: 4 Bytes total
        # 2 Bytes: Tage seit Referenz (0-65535)
        # 1 Byte:  Gültigkeitsmonate (1-255)  
        # 1 Byte:  Prüfsumme
        
        # Basis-Daten packen
        base_data = struct.pack('<H B', days_since_ref, validity_months)
        
        # Einfache Prüfsumme berechnen
        checksum = (sum(base_data) + sum(self.device_key[:4])) % 256
        
        # Vollständige Daten
        full_data = base_data + bytes([checksum])
        
        # Hash berechnen (viel schneller als HMAC)
        hash_val = self._simple_hash(full_data)
        
        # PIN aus Hash ableiten
        pin_int = hash_val % 100000000
        
        return str(pin_int).zfill(8)
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        """
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, 255)


class EmbeddedPinValidator:
    """
    Embedded-optimierter PIN-Validator mit intelligenter Suche
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 16:
            raise ValueError("Device key muss genau 16 Bytes lang sein")
        self.device_key = device_key
        
    def _simple_hash(self, data: bytes) -> int:
        """
        Gleiche Hash-Funktion wie im Generator
        """
        seed = struct.unpack('<I', self.device_key[:4])[0]
        
        hash_val = seed
        for i, byte in enumerate(data):
            hash_val ^= byte << (i % 24)
            hash_val = ((hash_val << 13) | (hash_val >> 19)) & 0xFFFFFFFF
            hash_val ^= struct.unpack('<I', self.device_key[4 + (i % 3) * 4:8 + (i % 3) * 4])[0]
            
        return hash_val
    
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert PIN mit optimierter Suche für Embedded Systeme
        
        Strategie:
        1. Teste zuerst häufige/wahrscheinliche Werte
        2. Begrenze Suchraum auf realistische Bereiche
        3. Verwende intelligente Heuristiken
        """
        if current_date is None:
            current_date = datetime.now()
            
        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }
        
        pin_int = int(pin)
        ref_date = datetime(2020, 1, 1)
        current_days = (current_date.date() - ref_date.date()).days
        
        # Optimierte Suchstrategie für Embedded Systeme
        
        # 1. Teste unbegrenzte Lizenz zuerst (1 Berechnung)
        special_days = (datetime(2099, 12, 31).date() - ref_date.date()).days
        if self._test_combination(pin_int, special_days, 255):
            return {
                'valid': True,
                'unlimited': True,
                'purchase_date': datetime(2099, 12, 31),
                'validity_months': 255,
                'expires': None
            }
        
        # 2. Teste aktuelle/nahe Daten mit häufigen Gültigkeiten (max. 90 Berechnungen)
        # Suche ±3 Jahre um aktuelles Datum
        start_days = max(0, current_days - 1095)  # 3 Jahre zurück
        end_days = min(65535, current_days + 1095)  # 3 Jahre voraus
        
        common_months = [1, 3, 6, 12, 18, 24, 36, 48, 60]  # 9 häufige Werte
        
        # Teste jeden 30. Tag (ca. monatlich) mit häufigen Gültigkeiten
        for days in range(start_days, end_days, 30):
            for months in common_months:
                if self._test_combination(pin_int, days, months):
                    purchase_date = ref_date + timedelta(days=days)
                    expiry_date = purchase_date + timedelta(days=months * 30)
                    is_valid = current_date <= expiry_date
                    
                    return {
                        'valid': is_valid,
                        'unlimited': False,
                        'purchase_date': purchase_date,
                        'validity_months': months,
                        'expires': expiry_date,
                        'days_remaining': (expiry_date - current_date).days if is_valid else 0
                    }
        
        # 3. Erweiterte Suche nur bei Bedarf (max. 255 weitere Berechnungen)
        # Teste alle Gültigkeitsmonate für wahrscheinliche Daten
        likely_dates = [
            current_days,  # Heute
            current_days - 365,  # Vor 1 Jahr
            current_days - 730,  # Vor 2 Jahren
            current_days + 365,  # In 1 Jahr
        ]
        
        for days in likely_dates:
            if 0 <= days <= 65535:
                for months in range(1, 256):
                    if self._test_combination(pin_int, days, months):
                        purchase_date = ref_date + timedelta(days=days)
                        
                        if months == 255 and purchase_date.year == 2099:
                            return {
                                'valid': True,
                                'unlimited': True,
                                'purchase_date': purchase_date,
                                'validity_months': months,
                                'expires': None
                            }
                        
                        expiry_date = purchase_date + timedelta(days=months * 30)
                        is_valid = current_date <= expiry_date
                        
                        return {
                            'valid': is_valid,
                            'unlimited': False,
                            'purchase_date': purchase_date,
                            'validity_months': months,
                            'expires': expiry_date,
                            'days_remaining': (expiry_date - current_date).days if is_valid else 0
                        }
        
        return {
            'valid': False,
            'error': 'PIN konnte nicht validiert werden'
        }
    
    def _test_combination(self, target_pin: int, days: int, months: int) -> bool:
        """
        Testet eine spezifische Kombination von Tagen und Monaten
        Optimiert für minimale Berechnungen
        """
        try:
            # Basis-Daten packen
            base_data = struct.pack('<H B', days, months)
            
            # Prüfsumme berechnen
            checksum = (sum(base_data) + sum(self.device_key[:4])) % 256
            
            # Vollständige Daten
            full_data = base_data + bytes([checksum])
            
            # Hash berechnen
            hash_val = self._simple_hash(full_data)
            
            # PIN ableiten
            calculated_pin = hash_val % 100000000
            
            return calculated_pin == target_pin
            
        except (struct.error, ValueError):
            return False


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 16-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(16)


# Performance-Vergleich und Benchmarking
def benchmark_validation(device_key: bytes, num_tests: int = 100):
    """
    Benchmarkt die Validierungsperformance
    """
    import time
    
    generator = EmbeddedPinGenerator(device_key)
    validator = EmbeddedPinValidator(device_key)
    
    # Generiere Test-PINs
    test_pins = []
    test_date = datetime(2024, 6, 15)
    
    for months in [1, 6, 12, 24]:
        pin = generator.generate_pin(test_date, months)
        test_pins.append(pin)
    
    # Füge unbegrenzten PIN hinzu
    test_pins.append(generator.generate_unlimited_pin())
    
    # Benchmark
    start_time = time.time()
    
    for _ in range(num_tests):
        for pin in test_pins:
            result = validator.validate_pin(pin, test_date)
            assert result['valid'], f"PIN {pin} sollte gültig sein"
    
    end_time = time.time()
    
    total_validations = num_tests * len(test_pins)
    avg_time_ms = (end_time - start_time) * 1000 / total_validations
    
    print(f"Embedded PIN-System Benchmark:")
    print(f"- {total_validations} Validierungen in {(end_time - start_time)*1000:.1f}ms")
    print(f"- Durchschnitt: {avg_time_ms:.2f}ms pro Validierung")
    print(f"- Geschätzte CPU-Zyklen: ~{avg_time_ms * 1000:.0f} (bei 1MHz)")


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = EmbeddedPinGenerator(device_key)
    validator = EmbeddedPinValidator(device_key)
    
    # PIN für 12 Monate ab heute generieren
    purchase_date = datetime.now()
    pin = generator.generate_pin(purchase_date, 12)
    print(f"Generierter PIN: {pin}")
    
    # PIN validieren
    result = validator.validate_pin(pin)
    print(f"Validierungsergebnis: {result}")
    
    # Unbegrenzten PIN generieren
    unlimited_pin = generator.generate_unlimited_pin()
    print(f"Unbegrenzter PIN: {unlimited_pin}")
    
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzte Validierung: {unlimited_result}")
    
    # Performance-Test
    print("\n" + "="*50)
    benchmark_validation(device_key, 20)
