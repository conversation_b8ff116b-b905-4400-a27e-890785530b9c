#!/usr/bin/env python3
"""
Test-Suite für das zeitbasierte PIN-System
"""

import unittest
from datetime import datetime, timedelta
import os
from pin_generator_final import TimedPinGenerator, TimedPinValidator, generate_device_key


class TestTimedPinSystem(unittest.TestCase):
    """
    Tests für das zeitbasierte PIN-System
    """
    
    def setUp(self):
        """Setup für jeden Test"""
        self.device_key = b'0123456789abcdef' * 2  # 32 Bytes Test-Schlüssel
        self.generator = TimedPinGenerator(self.device_key)
        self.validator = TimedPinValidator(self.device_key)
        
    def test_device_key_validation(self):
        """Test der Device-Key Validierung"""
        # <PERSON>u kurzer <PERSON>
        with self.assertRaises(ValueError):
            TimedPinGenerator(b'short_key')
            
        # <PERSON>u langer <PERSON>hl<PERSON>
        with self.assertRaises(ValueError):
            TimedPinGenerator(b'x' * 33)
            
        # Korrekt<PERSON>hlüssel
        generator = TimedPinGenerator(b'x' * 32)
        self.assertIsNotNone(generator)
        
    def test_pin_generation_format(self):
        """Test des PIN-Formats"""
        purchase_date = datetime(2024, 1, 1)
        pin = self.generator.generate_pin(purchase_date, 12)
        
        # PIN muss 8 Stellen haben
        self.assertEqual(len(pin), 8)
        # PIN muss nur Ziffern enthalten
        self.assertTrue(pin.isdigit())
        
    def test_pin_validation_basic(self):
        """Test der grundlegenden PIN-Validierung"""
        purchase_date = datetime(2024, 1, 1)
        validity_months = 12
        
        pin = self.generator.generate_pin(purchase_date, validity_months)
        
        # Validierung zum Kaufdatum (sollte gültig sein)
        result = self.validator.validate_pin(pin, purchase_date)
        self.assertTrue(result['valid'])
        self.assertEqual(result['validity_months'], validity_months)
        self.assertFalse(result['unlimited'])
        
    def test_pin_expiry(self):
        """Test der PIN-Ablauflogik"""
        purchase_date = datetime(2024, 1, 1)
        validity_months = 6
        
        pin = self.generator.generate_pin(purchase_date, validity_months)
        
        # Test vor Ablauf
        test_date_valid = purchase_date + timedelta(days=30)  # 1 Monat später
        result = self.validator.validate_pin(pin, test_date_valid)
        self.assertTrue(result['valid'])
        
        # Test nach Ablauf
        test_date_expired = purchase_date + timedelta(days=200)  # 6+ Monate später
        result = self.validator.validate_pin(pin, test_date_expired)
        self.assertFalse(result['valid'])
        
    def test_unlimited_pin(self):
        """Test des unbegrenzten PINs"""
        unlimited_pin = self.generator.generate_unlimited_pin()
        
        # Validierung in ferner Zukunft
        future_date = datetime(2050, 1, 1)
        result = self.validator.validate_pin(unlimited_pin, future_date)
        
        self.assertTrue(result['valid'])
        self.assertTrue(result['unlimited'])
        self.assertIsNone(result['expires'])
        
    def test_invalid_pin_format(self):
        """Test ungültiger PIN-Formate"""
        # Zu kurz
        result = self.validator.validate_pin("1234567")
        self.assertFalse(result['valid'])
        self.assertIn('error', result)
        
        # Zu lang
        result = self.validator.validate_pin("123456789")
        self.assertFalse(result['valid'])
        
        # Nicht-numerisch
        result = self.validator.validate_pin("1234567a")
        self.assertFalse(result['valid'])
        
    def test_device_specific_validation(self):
        """Test der gerätespezifischen Validierung"""
        purchase_date = datetime(2024, 1, 1)
        pin = self.generator.generate_pin(purchase_date, 12)
        
        # Anderer Device-Key
        other_device_key = b'fedcba9876543210' * 2
        other_validator = TimedPinValidator(other_device_key)
        
        # PIN sollte mit anderem Schlüssel nicht gültig sein
        result = other_validator.validate_pin(pin, purchase_date)
        self.assertFalse(result['valid'])
        
    def test_crc_integrity(self):
        """Test der CRC-Integrität"""
        generator = TimedPinGenerator(self.device_key)
        validator = TimedPinValidator(self.device_key)
        
        purchase_date = datetime(2024, 6, 15)
        pin = generator.generate_pin(purchase_date, 24)
        
        result = validator.validate_pin(pin, purchase_date)
        self.assertTrue(result['valid'])
        
    def test_date_range_limits(self):
        """Test der Datumsgrenzen"""
        # Sehr frühes Datum
        early_date = datetime(2020, 1, 1)
        pin = self.generator.generate_pin(early_date, 12)
        result = self.validator.validate_pin(pin, early_date)
        self.assertTrue(result['valid'])

        # Datum innerhalb des Suchbereichs (2020-2030)
        normal_date = datetime(2025, 6, 15)
        pin = self.generator.generate_pin(normal_date, 12)
        result = self.validator.validate_pin(pin, normal_date)
        self.assertTrue(result['valid'])
        
    def test_validity_months_range(self):
        """Test der Gültigkeitsmonate-Grenzen"""
        purchase_date = datetime(2024, 1, 1)
        
        # Minimum: 1 Monat
        pin = self.generator.generate_pin(purchase_date, 1)
        result = self.validator.validate_pin(pin, purchase_date)
        self.assertTrue(result['valid'])
        self.assertEqual(result['validity_months'], 1)
        
        # Maximum: 60 Monate (innerhalb der häufigen Werte)
        pin = self.generator.generate_pin(purchase_date, 60)
        result = self.validator.validate_pin(pin, purchase_date)
        self.assertTrue(result['valid'])
        self.assertEqual(result['validity_months'], 60)
        
        # Ungültig: 0 Monate
        with self.assertRaises(ValueError):
            self.generator.generate_pin(purchase_date, 0)
            
        # Ungültig: > 255 Monate
        with self.assertRaises(ValueError):
            self.generator.generate_pin(purchase_date, 256)
            
    def test_reproducibility(self):
        """Test der Reproduzierbarkeit"""
        purchase_date = datetime(2024, 3, 15)
        validity_months = 18
        
        # Gleiche Parameter sollten gleichen PIN erzeugen
        pin1 = self.generator.generate_pin(purchase_date, validity_months)
        pin2 = self.generator.generate_pin(purchase_date, validity_months)
        
        self.assertEqual(pin1, pin2)
        
    def test_different_parameters_different_pins(self):
        """Test dass verschiedene Parameter verschiedene PINs erzeugen"""
        base_date = datetime(2024, 1, 1)
        
        pin1 = self.generator.generate_pin(base_date, 12)
        pin2 = self.generator.generate_pin(base_date, 24)  # Andere Gültigkeit
        pin3 = self.generator.generate_pin(base_date + timedelta(days=1), 12)  # Anderes Datum
        
        # Alle PINs sollten unterschiedlich sein
        self.assertNotEqual(pin1, pin2)
        self.assertNotEqual(pin1, pin3)
        self.assertNotEqual(pin2, pin3)
        
    def test_random_device_key_generation(self):
        """Test der zufälligen Device-Key Generierung"""
        key1 = generate_device_key()
        key2 = generate_device_key()
        
        # Schlüssel sollten 32 Bytes lang sein
        self.assertEqual(len(key1), 32)
        self.assertEqual(len(key2), 32)
        
        # Schlüssel sollten unterschiedlich sein
        self.assertNotEqual(key1, key2)


class TestRealWorldScenarios(unittest.TestCase):
    """
    Tests für realistische Anwendungsszenarien
    """
    
    def setUp(self):
        """Setup für jeden Test"""
        self.device_key = generate_device_key()
        self.generator = TimedPinGenerator(self.device_key)
        self.validator = TimedPinValidator(self.device_key)
        
    def test_annual_license_scenario(self):
        """Test eines jährlichen Lizenz-Szenarios"""
        # Kunde kauft am 1. Januar 2024 eine Jahreslizenz
        purchase_date = datetime(2024, 1, 1)
        pin = self.generator.generate_pin(purchase_date, 12)
        
        # Test verschiedene Zeitpunkte im Jahr
        test_dates = [
            (datetime(2024, 6, 1), True),   # Mitte des Jahres - gültig
            (datetime(2024, 11, 30), True), # Ende des Jahres - gültig
            (datetime(2025, 1, 2), False),  # Nach Ablauf - ungültig
        ]
        
        for test_date, expected_valid in test_dates:
            result = self.validator.validate_pin(pin, test_date)
            self.assertEqual(result['valid'], expected_valid, 
                           f"Fehler bei Datum {test_date}")
            
    def test_internal_unlimited_scenario(self):
        """Test des internen unbegrenzten Szenarios"""
        unlimited_pin = self.generator.generate_unlimited_pin()
        
        # Test in verschiedenen Jahren
        test_dates = [
            datetime(2024, 1, 1),
            datetime(2030, 6, 15),
            datetime(2050, 12, 31),
        ]
        
        for test_date in test_dates:
            result = self.validator.validate_pin(unlimited_pin, test_date)
            self.assertTrue(result['valid'])
            self.assertTrue(result['unlimited'])
            
    def test_field_update_compatibility(self):
        """Test der Kompatibilität für Feld-Updates"""
        # Simuliere ein Gerät im Feld mit bestehendem PIN
        purchase_date = datetime(2023, 6, 1)
        pin = self.generator.generate_pin(purchase_date, 18)
        
        # Validierung nach Software-Update (verschiedene Zeitpunkte)
        current_date = datetime(2024, 8, 1)  # 14 Monate später
        result = self.validator.validate_pin(pin, current_date)
        
        # PIN sollte noch 4 Monate gültig sein
        self.assertTrue(result['valid'])
        self.assertGreater(result['days_remaining'], 0)


if __name__ == '__main__':
    # Führe alle Tests aus
    unittest.main(verbosity=2)
