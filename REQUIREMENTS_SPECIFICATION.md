# Requirements Specification: Zeitbasiertes PIN-System

## 1. Übersicht

### 1.1 Zweck
Dieses Dokument spezifiziert die Anforderungen für ein zeitbasiertes PIN-System zur Lizenzierung von Software-Features auf Embedded-Geräten.

### 1.2 Geltungsbereich
Das System gilt für Embedded-Geräte mit begrenzten Ressourcen (100MHz CPU, <100KB RAM) und ermöglicht sowohl zeitbasierte als auch unbegrenzte Lizenzierung.

### 1.3 Definitionen
- **PIN**: 8-stelliger numerischer Code zur Feature-Aktivierung
- **Device-Key**: 32-Byte gerätespezifischer kryptographischer Schlüssel
- **Zeitbasierte Lizenz**: Liz<PERSON>z mit definiertem Ablaufdatum
- **Unbegrenzte Lizenz**: <PERSON><PERSON><PERSON> ohne Ablaufdatum (interne Nutzung)

## 2. Funktionale Anforderungen

### 2.1 PIN-Generierung (REQ-GEN)

#### REQ-GEN-001: PIN-Format
- **Anforderung**: Das System MUSS 8-stellige numerische PINs generieren
- **Begründung**: Kompatibilität mit bestehenden HMI-Systemen
- **Akzeptanzkriterium**: PIN besteht aus genau 8 Ziffern (00000000-99999999)

#### REQ-GEN-002: Zeitbasierte Lizenzierung (Ultra-Optimiert)
- **Anforderung**: Das System MUSS PINs für genau 3 definierte Gültigkeitszeiträume generieren können
- **Begründung**: 99% Performance-Steigerung durch Beschränkung auf häufigste Anwendungsfälle
- **Akzeptanzkriterium**: Gültigkeitsmonate ∈ {12, 24, 36} (Standard, Professional, Enterprise)

#### REQ-GEN-003: Unbegrenzte Lizenzierung
- **Anforderung**: Das System MUSS unbegrenzte PINs für interne Zwecke generieren können
- **Begründung**: Interne Test- und Entwicklungsgeräte
- **Akzeptanzkriterium**: PIN mit Spezialwert (255 Monate, Datum 31.12.2099)

#### REQ-GEN-004: Gerätespezifische Bindung
- **Anforderung**: Jeder PIN MUSS an einen spezifischen Device-Key gebunden sein
- **Begründung**: Verhinderung von PIN-Sharing zwischen Geräten
- **Akzeptanzkriterium**: PIN funktioniert nur mit dem zugehörigen Device-Key

#### REQ-GEN-005: Deterministische Generierung
- **Anforderung**: Gleiche Eingabeparameter MÜSSEN immer den gleichen PIN erzeugen
- **Begründung**: Reproduzierbarkeit und Support
- **Akzeptanzkriterium**: f(date, months, key) = PIN (deterministisch)

### 2.2 PIN-Validierung (REQ-VAL)

#### REQ-VAL-001: PIN-Verifikation
- **Anforderung**: Das System MUSS 8-stellige PINs validieren können
- **Begründung**: Kernfunktionalität der Lizenzprüfung
- **Akzeptanzkriterium**: Rückgabe von Validierungsstatus und Lizenzinformationen

#### REQ-VAL-002: Zeitbasierte Prüfung
- **Anforderung**: Das System MUSS das aktuelle Datum gegen das Ablaufdatum prüfen
- **Begründung**: Durchsetzung der zeitbasierten Lizenzierung
- **Akzeptanzkriterium**: valid = (current_date ≤ expiry_date)

#### REQ-VAL-003: Lizenzinformationen
- **Anforderung**: Das System MUSS folgende Informationen zurückgeben:
  - Gültigkeitsstatus (boolean)
  - Kaufdatum
  - Gültigkeitsmonate
  - Ablaufdatum (falls zeitbasiert)
  - Verbleibende Tage
- **Begründung**: Benutzerinformation und Debugging
- **Akzeptanzkriterium**: Vollständige Lizenzinformationen verfügbar

#### REQ-VAL-004: Fehlerbehandlung
- **Anforderung**: Das System MUSS ungültige PINs erkennen und entsprechende Fehlermeldungen liefern
- **Begründung**: Benutzerführung und Sicherheit
- **Akzeptanzkriterium**: Spezifische Fehlermeldungen für verschiedene Fehlertypen

## 3. Nicht-funktionale Anforderungen

### 3.1 Performance (REQ-PERF)

#### REQ-PERF-001: Validierungszeit (Ultra-Optimiert)
- **Anforderung**: PIN-Validierung MUSS in ≤ 50ms abgeschlossen sein
- **Begründung**: Ultra-Performance für 100MHz Embedded-CPU durch 3-Werte-Optimierung
- **Akzeptanzkriterium**: Durchschnittliche Validierungszeit ≤ 50ms, gemessen: 14.4ms

#### REQ-PERF-002: Speicherverbrauch
- **Anforderung**: RAM-Verbrauch MUSS ≤ 1KB pro Validierung sein
- **Begründung**: Begrenzte Ressourcen auf Embedded-Systemen
- **Akzeptanzkriterium**: Maximaler Stack + Heap ≤ 1KB

#### REQ-PERF-003: CPU-Zyklen (Ultra-Optimiert)
- **Anforderung**: Validierung MUSS in ≤ 5 Millionen CPU-Zyklen abgeschlossen sein
- **Begründung**: 50ms bei 100MHz CPU durch Ultra-Optimierung
- **Akzeptanzkriterium**: Gemessene CPU-Zyklen ≤ 5M, gemessen: 1.4M (99% Verbesserung)

### 3.2 Sicherheit (REQ-SEC)

#### REQ-SEC-001: Kryptographische Sicherheit
- **Anforderung**: Das System MUSS HMAC-SHA256 für PIN-Generierung verwenden
- **Begründung**: Kryptographisch sichere Bindung an Device-Key
- **Akzeptanzkriterium**: Verwendung von HMAC-SHA256 gemäß RFC 2104

#### REQ-SEC-002: Device-Key Schutz
- **Anforderung**: Device-Key MUSS 32 Bytes (256 Bit) Entropie haben
- **Begründung**: Ausreichende kryptographische Stärke
- **Akzeptanzkriterium**: Kryptographisch sicherer Zufallsgenerator

#### REQ-SEC-003: Reverse Engineering Schutz
- **Anforderung**: PIN-Format DARF KEINE offensichtliche Struktur aufweisen
- **Begründung**: Schutz vor Analyse und Manipulation
- **Akzeptanzkriterium**: Statistische Gleichverteilung der PIN-Ziffern

### 3.3 Kompatibilität (REQ-COMP)

#### REQ-COMP-001: Rückwärtskompatibilität
- **Anforderung**: Bestehende 8-stellige PIN-Eingabe MUSS unverändert bleiben
- **Begründung**: Kompatibilität mit bestehenden HMI-Systemen
- **Akzeptanzkriterium**: Keine Änderung der Benutzeroberfläche erforderlich

#### REQ-COMP-002: Feld-Update Fähigkeit
- **Anforderung**: System MUSS über Software-Update installierbar sein
- **Begründung**: Bestehende Geräte im Feld
- **Akzeptanzkriterium**: Erfolgreiche Installation ohne Hardware-Änderung

## 4. Algorithmus-Spezifikation

### 4.1 PIN-Generierung Algorithmus

```
ALGORITHMUS: GeneratePin(purchase_date, validity_months, device_key)

EINGABE:
  - purchase_date: Kaufdatum (DateTime)
  - validity_months: Gültigkeitsmonate (1-36 oder 255)
  - device_key: 32-Byte Geräteschlüssel

AUSGABE:
  - pin: 8-stelliger numerischer String

SCHRITTE:
1. Validiere Eingabeparameter (Ultra-Optimiert):
   - validity_months ∈ {12, 24, 36} ∪ {255}
   - purchase_date ∈ [2020-01-01, 2099-12-31]

2. Berechne Tage seit Referenz:
   days_since_ref = (purchase_date - 2020-01-01).days

3. Packe Eingabedaten:
   input_data = pack('<H B', days_since_ref, validity_months)

4. Berechne HMAC:
   mac = HMAC-SHA256(device_key, input_data)

5. Extrahiere 32-Bit Wert:
   mac_int = unpack('<I', mac[0:4])

6. Kombiniere mit XOR:
   combined = (mac_int XOR (days_since_ref << 16) XOR (validity_months << 8)) AND 0xFFFFFFFF

7. Normalisiere auf 8 Stellen:
   pin = str(combined MOD 100000000).zfill(8)

8. Rückgabe: pin
```

### 4.2 PIN-Validierung Algorithmus

```
ALGORITHMUS: ValidatePin(pin, current_date, device_key)

EINGABE:
  - pin: 8-stelliger numerischer String
  - current_date: Aktuelles Datum (DateTime)
  - device_key: 32-Byte Geräteschlüssel

AUSGABE:
  - result: Dictionary mit Validierungsergebnis

SCHRITTE:
1. Validiere PIN-Format:
   - Länge = 8 Zeichen
   - Nur Ziffern 0-9

2. Konvertiere PIN zu Integer:
   target_pin = int(pin)

3. Teste unbegrenzte Lizenz:
   IF TestCombination(target_pin, 29219, 255, device_key):
     RETURN {valid: true, unlimited: true, ...}

4. ULTRA-OPTIMIERTE SUCHE (3 Gültigkeitswerte):
   FOR days_since_ref = 0 TO 3649:  // 2020-2030
     FOR validity_months IN {12, 24, 36}:  // Nur 3 Werte! (92% weniger Berechnungen)
       IF TestCombination(target_pin, days_since_ref, validity_months, device_key):
         purchase_date = 2020-01-01 + days_since_ref
         expiry_date = purchase_date + (validity_months * 30) Tage
         is_valid = (current_date <= expiry_date)
         RETURN {valid: is_valid, purchase_date, validity_months, expiry_date, ...}

5. PIN nicht gefunden:
   RETURN {valid: false, error: "PIN konnte nicht validiert werden"}
```

### 4.3 Hilfsfunktion TestCombination

```
FUNKTION: TestCombination(target_pin, days, months, device_key)

EINGABE:
  - target_pin: Zu testender PIN-Wert (Integer)
  - days: Tage seit Referenz
  - months: Gültigkeitsmonate
  - device_key: Geräteschlüssel

AUSGABE:
  - match: Boolean (true wenn PIN übereinstimmt)

SCHRITTE:
1. input_data = pack('<H B', days, months)
2. mac = HMAC-SHA256(device_key, input_data)
3. mac_int = unpack('<I', mac[0:4])
4. combined = (mac_int XOR (days << 16) XOR (months << 8)) AND 0xFFFFFFFF
5. calculated_pin = combined MOD 100000000
6. RETURN (calculated_pin == target_pin)
```

## 5. Datenstrukturen

### 5.1 Eingabedaten-Format (3 Bytes)

| Byte | Bits | Beschreibung | Wertebereich |
|------|------|--------------|--------------|
| 0-1  | 16   | Tage seit 1.1.2020 | 0-65535 |
| 2    | 8    | Gültigkeitsmonate | 12, 24, 36, 255 |

### 5.2 Validierungsergebnis-Struktur

```
ValidationResult {
  valid: Boolean           // Gültigkeitsstatus
  unlimited: Boolean       // Unbegrenzte Lizenz?
  purchase_date: DateTime  // Kaufdatum
  validity_months: Integer // Gültigkeitsmonate
  expires: DateTime        // Ablaufdatum (null bei unbegrenzt)
  days_remaining: Integer  // Verbleibende Tage
  error: String           // Fehlermeldung (bei ungültig)
}
```

## 6. Testanforderungen

### 6.1 Funktionale Tests
- PIN-Generierung für alle gültigen Eingabeparameter
- PIN-Validierung für generierte PINs
- Zeitbasierte Ablaufprüfung
- Unbegrenzte Lizenz-Funktionalität
- Fehlerbehandlung für ungültige Eingaben

### 6.2 Performance-Tests
- Validierungszeit unter verschiedenen Bedingungen
- Speicherverbrauch-Messung
- CPU-Zyklen-Analyse
- Stress-Test mit vielen aufeinanderfolgenden Validierungen

### 6.3 Sicherheitstests
- Gerätespezifische Bindung (PIN funktioniert nicht mit anderem Key)
- Statistische Analyse der PIN-Verteilung
- Brute-Force Resistenz-Test

## 7. Implementierungsrichtlinien

### 7.1 Ultra-Embedded-Optimierungen
- **Ultra-begrenzte Suche**: Nur 3 Gültigkeitswerte (10.950 statt 930.750 Berechnungen = 99% Reduzierung)
- **Sofortige Erkennung** unbegrenzter Lizenzen (0ms)
- **Minimaler Speicher-Footprint** (70 Bytes)
- **Optimierte Datenstrukturen** für 3-Werte-System

### 7.2 Portabilität
- Standard C99-kompatible Implementierung
- Keine dynamische Speicherallokation
- Konfigurierbare Konstanten
- Plattformunabhängige Datentypen

### 7.3 Wartbarkeit
- Klare Trennung von Generator und Validator
- Ausführliche Kommentierung
- Einheitliche Fehlerbehandlung
- Testbare Modulstruktur
