#!/usr/bin/env python3
"""
Test für 15-Monats-PIN und Performance-Test
"""

from pin_generator_final import TimedPinGenerator, TimedPinValidator, generate_device_key
from datetime import datetime
import time

def test_15_months():
    """Test speziell für 15 Monate"""
    print("Test für 15-Monats-PIN:")
    print("=" * 30)
    
    device_key = generate_device_key()
    generator = TimedPinGenerator(device_key)
    validator = TimedPinValidator(device_key)
    
    purchase_date = datetime(2024, 6, 15)
    
    # PIN für 15 Monate generieren
    pin = generator.generate_pin(purchase_date, 15)
    print(f"Generierter PIN für 15 Monate: {pin}")
    
    # PIN validieren
    start_time = time.time()
    result = validator.validate_pin(pin, purchase_date)
    end_time = time.time()
    
    validation_time = (end_time - start_time) * 1000
    
    print(f"Validierungsergebnis: {result['valid']}")
    print(f"Validierungszeit: {validation_time:.1f}ms")
    
    if result['valid']:
        print(f"Kaufdatum: {result['purchase_date'].strftime('%d.%m.%Y')}")
        print(f"Gültigkeitsmonate: {result['validity_months']}")
        print(f"Ablaufdatum: {result['expires'].strftime('%d.%m.%Y')}")
    
    return result['valid']

def performance_test():
    """Performance-Test für verschiedene Gültigkeitswerte"""
    print("\nPerformance-Test für verschiedene Gültigkeitswerte:")
    print("=" * 55)
    
    device_key = generate_device_key()
    generator = TimedPinGenerator(device_key)
    validator = TimedPinValidator(device_key)
    
    purchase_date = datetime(2024, 6, 15)
    test_months = [1, 7, 12, 15, 23, 37, 50, 100, 200]
    
    for months in test_months:
        # PIN generieren
        pin = generator.generate_pin(purchase_date, months)
        
        # Zeit messen
        start_time = time.time()
        result = validator.validate_pin(pin, purchase_date)
        end_time = time.time()
        
        validation_time = (end_time - start_time) * 1000
        valid_status = result['valid']
        
        print(f"{months:3d} Monate - PIN: {pin} - Gültig: {valid_status} - Zeit: {validation_time:.1f}ms")

if __name__ == "__main__":
    # Test für 15 Monate
    success = test_15_months()
    
    if success:
        print("\n✅ 15-Monats-Test erfolgreich!")
    else:
        print("\n❌ 15-Monats-Test fehlgeschlagen!")
    
    # Performance-Test
    performance_test()
    
    print("\nAlle Tests abgeschlossen!")
