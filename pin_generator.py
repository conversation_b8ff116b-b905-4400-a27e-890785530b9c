#!/usr/bin/env python3
"""
Zeitbasiertes PIN-Generator System
Generiert 8-stellige PINs die Kaufdatum und Gültigkeitsdauer kodieren
"""

import struct
import hashlib
from datetime import datetime, timedelta
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import os
import hmac

class TimedPinGenerator:
    """
    Generator für zeitbasierte PINs mit AES-Verschlüsselung
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 32-Byte gerätespezifischer Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def _calculate_crc16(self, data: bytes) -> int:
        """
        Berechnet CRC16 für Datenintegrität
        """
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc & 0xFFFF
    
    def _pack_license_data(self, purchase_date: datetime, validity_months: int) -> bytes:
        """
        Packt Lizenzdaten in 8 Bytes
        
        Format:
        - 4 Bytes: Kaufdatum als Unix timestamp (Tage seit 1.1.2020)
        - 1 Byte: Gültigkeitsmonate (1-255)
        - 1 Byte: Reserved (0x00)
        - 2 Bytes: CRC16 des device_key
        """
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 0xFFFFFFFF:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs")
        
        if validity_months < 1 or validity_months > 255:
            raise ValueError("Gültigkeitsmonate müssen zwischen 1 und 255 liegen")
        
        # CRC des device_key berechnen
        device_crc = self._calculate_crc16(self.device_key)
        
        # Daten packen
        packed_data = struct.pack('<I B B H', 
                                days_since_ref, 
                                validity_months, 
                                0x00,  # Reserved
                                device_crc)
        
        return packed_data
    
    def _encrypt_data(self, data: bytes) -> bytes:
        """
        Verschlüsselt Daten mit AES-128-ECB
        """
        # Verwende ersten 16 Bytes des device_key als AES-Schlüssel
        aes_key = self.device_key[:16]
        
        cipher = Cipher(algorithms.AES(aes_key), modes.ECB(), backend=default_backend())
        encryptor = cipher.encryptor()
        
        # Daten auf 16 Bytes auffüllen (PKCS7 Padding)
        padded_data = data + bytes([16 - len(data)] * (16 - len(data)))
        
        encrypted = encryptor.update(padded_data) + encryptor.finalize()
        return encrypted
    
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN für gegebenes Kaufdatum und Gültigkeitsdauer

        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer in Monaten

        Returns:
            8-stelliger PIN als String
        """
        # Lizenzdaten packen
        license_data = self._pack_license_data(purchase_date, validity_months)

        # Verschlüsseln
        encrypted_data = self._encrypt_data(license_data)

        # Verwende alle 16 Bytes für bessere Eindeutigkeit
        # Kombiniere mit XOR für 8-Byte Ergebnis
        first_8 = encrypted_data[:8]
        second_8 = encrypted_data[8:]

        combined = bytes(a ^ b for a, b in zip(first_8, second_8))
        pin_int = int.from_bytes(combined, byteorder='big')

        # Auf 8 Stellen normalisieren
        pin_str = str(pin_int % 100000000).zfill(8)

        # Speichere die ursprünglichen verschlüsselten Daten für Validierung
        # (In echter Implementierung würde dies anders gelöst)
        self._last_encrypted_data = encrypted_data
        self._last_license_data = license_data

        return pin_str
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        
        Returns:
            8-stelliger PIN für unbegrenzte Nutzung
        """
        # Spezielle Werte für unbegrenzte Lizenz
        # Verwende maximale Werte als Indikator
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, 255)


class TimedPinValidator:
    """
    Validator für zeitbasierte PINs
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def _calculate_crc16(self, data: bytes) -> int:
        """
        Berechnet CRC16 für Datenintegrität
        """
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc & 0xFFFF
    
    def _decrypt_data(self, encrypted_data: bytes) -> bytes:
        """
        Entschlüsselt Daten mit AES-128-ECB
        """
        # Verwende ersten 16 Bytes des device_key als AES-Schlüssel
        aes_key = self.device_key[:16]
        
        cipher = Cipher(algorithms.AES(aes_key), modes.ECB(), backend=default_backend())
        decryptor = cipher.decryptor()
        
        decrypted = decryptor.update(encrypted_data) + decryptor.finalize()
        
        # PKCS7 Padding entfernen
        padding_length = decrypted[-1]
        return decrypted[:-padding_length]
    
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert einen PIN und gibt Lizenzinformationen zurück

        Args:
            pin: 8-stelliger PIN
            current_date: Aktuelles Datum (Standard: heute)

        Returns:
            Dictionary mit Validierungsergebnis und Lizenzinformationen
        """
        if current_date is None:
            current_date = datetime.now()

        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }

        try:
            # PIN zurück in ursprüngliche verschlüsselte Daten umwandeln
            pin_int = int(pin)

            # Versuche verschiedene Möglichkeiten, den ursprünglichen verschlüsselten Wert zu rekonstruieren
            # Da wir nur die ersten 8 Bytes der 16-Byte AES-Ausgabe verwenden
            for high_bytes in range(256):  # Versuche verschiedene Werte für die oberen Bytes
                # Rekonstruiere 8-Byte Wert
                full_int = pin_int + (high_bytes << 64)

                try:
                    # Konvertiere zu 8 Bytes
                    pin_bytes = full_int.to_bytes(8, byteorder='big')

                    # Erweitere auf 16 Bytes für AES (die anderen 8 Bytes sind unbekannt)
                    # Versuche verschiedene Kombinationen für die unbekannten Bytes
                    for low_bytes in range(0, 256, 16):  # Reduzierte Anzahl für Performance
                        extended_bytes = pin_bytes + low_bytes.to_bytes(8, byteorder='big')

                        try:
                            # Entschlüsseln
                            decrypted_data = self._decrypt_data(extended_bytes)

                            if len(decrypted_data) < 8:
                                continue

                            # Daten entpacken
                            days_since_ref, validity_months, reserved, stored_crc = struct.unpack('<I B B H', decrypted_data[:8])

                            # CRC validieren
                            expected_crc = self._calculate_crc16(self.device_key)
                            if stored_crc != expected_crc:
                                continue

                            # Plausibilitätsprüfung der Daten
                            if validity_months == 0 or validity_months > 255:
                                continue

                            # Kaufdatum berechnen
                            ref_date = datetime(2020, 1, 1)
                            purchase_date = ref_date + timedelta(days=days_since_ref)

                            # Plausibilitätsprüfung des Datums
                            if purchase_date.year < 2020 or purchase_date.year > 2100:
                                continue

                            # Ablaufdatum berechnen
                            if validity_months == 255 and purchase_date.year == 2099:
                                # Unbegrenzte Lizenz
                                return {
                                    'valid': True,
                                    'unlimited': True,
                                    'purchase_date': purchase_date,
                                    'validity_months': validity_months,
                                    'expires': None
                                }

                            expiry_date = purchase_date + timedelta(days=validity_months * 30)

                            # Gültigkeit prüfen
                            is_valid = current_date <= expiry_date

                            return {
                                'valid': is_valid,
                                'unlimited': False,
                                'purchase_date': purchase_date,
                                'validity_months': validity_months,
                                'expires': expiry_date,
                                'days_remaining': (expiry_date - current_date).days if is_valid else 0
                            }

                        except Exception:
                            continue

                except (ValueError, OverflowError):
                    continue

            return {
                'valid': False,
                'error': 'PIN konnte nicht entschlüsselt werden'
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f'Validierungsfehler: {str(e)}'
            }


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 32-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(32)


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = TimedPinGenerator(device_key)
    validator = TimedPinValidator(device_key)
    
    # PIN für 12 Monate ab heute generieren
    purchase_date = datetime.now()
    pin = generator.generate_pin(purchase_date, 12)
    print(f"Generierter PIN: {pin}")
    
    # PIN validieren
    result = validator.validate_pin(pin)
    print(f"Validierungsergebnis: {result}")
    
    # Unbegrenzten PIN generieren
    unlimited_pin = generator.generate_unlimited_pin()
    print(f"Unbegrenzter PIN: {unlimited_pin}")
    
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzte Validierung: {unlimited_result}")
