#!/usr/bin/env python3
"""
Zeitbasiertes PIN-Generator System (Version 2)
Robuste Implementierung mit HMAC-basierter Validierung
"""

import struct
import hashlib
import hmac
from datetime import datetime, timedelta
import os


class TimedPinGenerator:
    """
    Generator für zeitbasierte PINs mit HMAC-Validierung
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 32-Byte gerätespezifischer Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def _pack_license_data(self, purchase_date: datetime, validity_months: int) -> bytes:
        """
        Packt Lizenzdaten in kompaktes Format
        
        Format (6 Bytes):
        - 4 Bytes: Kaufdatum als Tage seit 1.1.2020
        - 2 Bytes: Gültigkeitsmonate (1-65535)
        """
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 0xFFFFFFFF:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs")
        
        if validity_months < 1 or validity_months > 65535:
            raise ValueError("Gültigkeitsmonate müssen zwischen 1 und 65535 liegen")
        
        # Daten packen (Little Endian)
        packed_data = struct.pack('<I H', days_since_ref, validity_months)
        
        return packed_data
    
    def _generate_hmac(self, license_data: bytes) -> bytes:
        """
        Generiert HMAC für Lizenzdaten
        """
        return hmac.new(self.device_key, license_data, hashlib.sha256).digest()
    
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN für gegebenes Kaufdatum und Gültigkeitsdauer
        
        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer in Monaten
            
        Returns:
            8-stelliger PIN als String
        """
        # Lizenzdaten packen
        license_data = self._pack_license_data(purchase_date, validity_months)
        
        # HMAC generieren
        mac = self._generate_hmac(license_data)
        
        # Kombiniere Lizenzdaten mit ersten 2 Bytes des HMAC
        combined_data = license_data + mac[:2]  # 6 + 2 = 8 Bytes
        
        # In 64-Bit Integer umwandeln
        pin_int = struct.unpack('<Q', combined_data)[0]
        
        # Auf 8 Stellen normalisieren
        pin_str = str(pin_int % 100000000).zfill(8)
        
        return pin_str
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        
        Returns:
            8-stelliger PIN für unbegrenzte Nutzung
        """
        # Spezielle Werte für unbegrenzte Lizenz
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, 65535)


class TimedPinValidator:
    """
    Validator für zeitbasierte PINs
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def _generate_hmac(self, license_data: bytes) -> bytes:
        """
        Generiert HMAC für Lizenzdaten
        """
        return hmac.new(self.device_key, license_data, hashlib.sha256).digest()
    
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert einen PIN und gibt Lizenzinformationen zurück
        
        Args:
            pin: 8-stelliger PIN
            current_date: Aktuelles Datum (Standard: heute)
            
        Returns:
            Dictionary mit Validierungsergebnis und Lizenzinformationen
        """
        if current_date is None:
            current_date = datetime.now()
            
        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }
        
        try:
            # PIN zurück in 64-Bit Integer umwandeln
            pin_int = int(pin)
            
            # Versuche verschiedene Offsets (da wir modulo 100000000 verwendet haben)
            for offset in range(100):  # Begrenzte Anzahl für Performance
                test_int = pin_int + (offset * 100000000)
                
                try:
                    # In 8 Bytes umwandeln
                    combined_data = struct.pack('<Q', test_int)
                    
                    # Aufteilen in Lizenzdaten (6 Bytes) und HMAC-Teil (2 Bytes)
                    license_data = combined_data[:6]
                    stored_hmac_part = combined_data[6:8]
                    
                    # HMAC berechnen und vergleichen
                    calculated_hmac = self._generate_hmac(license_data)
                    expected_hmac_part = calculated_hmac[:2]
                    
                    if stored_hmac_part != expected_hmac_part:
                        continue
                    
                    # Lizenzdaten entpacken
                    days_since_ref, validity_months = struct.unpack('<I H', license_data)
                    
                    # Plausibilitätsprüfung
                    if validity_months == 0:
                        continue
                        
                    # Kaufdatum berechnen
                    ref_date = datetime(2020, 1, 1)
                    purchase_date = ref_date + timedelta(days=days_since_ref)
                    
                    # Plausibilitätsprüfung des Datums
                    if purchase_date.year < 2020 or purchase_date.year > 2100:
                        continue
                    
                    # Ablaufdatum berechnen
                    if validity_months == 65535 and purchase_date.year == 2099:
                        # Unbegrenzte Lizenz
                        return {
                            'valid': True,
                            'unlimited': True,
                            'purchase_date': purchase_date,
                            'validity_months': validity_months,
                            'expires': None
                        }
                    
                    expiry_date = purchase_date + timedelta(days=validity_months * 30)
                    
                    # Gültigkeit prüfen
                    is_valid = current_date <= expiry_date
                    
                    return {
                        'valid': is_valid,
                        'unlimited': False,
                        'purchase_date': purchase_date,
                        'validity_months': validity_months,
                        'expires': expiry_date,
                        'days_remaining': (expiry_date - current_date).days if is_valid else 0
                    }
                    
                except (struct.error, ValueError, OverflowError):
                    continue
            
            return {
                'valid': False,
                'error': 'PIN konnte nicht validiert werden'
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'Validierungsfehler: {str(e)}'
            }


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 32-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(32)


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = TimedPinGenerator(device_key)
    validator = TimedPinValidator(device_key)
    
    # PIN für 12 Monate ab heute generieren
    purchase_date = datetime.now()
    pin = generator.generate_pin(purchase_date, 12)
    print(f"Generierter PIN: {pin}")
    
    # PIN validieren
    result = validator.validate_pin(pin)
    print(f"Validierungsergebnis: {result}")
    
    # Unbegrenzten PIN generieren
    unlimited_pin = generator.generate_unlimited_pin()
    print(f"Unbegrenzter PIN: {unlimited_pin}")
    
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzte Validierung: {unlimited_result}")
