#!/usr/bin/env python3
"""
Zeitbasiertes PIN-Generator System (Finale Version)
Einfache aber sichere Implementierung mit direkter Kodierung
"""

import struct
import hashlib
import hmac
from datetime import datetime, timedelta
import os


class TimedPinGenerator:
    """
    Generator für zeitbasierte PINs
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 32-Byte gerätespezifischer Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def _calculate_checksum(self, data: bytes) -> int:
        """
        Berechnet eine einfache Prüfsumme
        """
        return sum(data) % 256
        
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN für gegebenes Kaufdatum und Gültigkeitsdauer
        
        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer in Monaten
            
        Returns:
            8-stelliger PIN als String
        """
        if validity_months < 1 or validity_months > 255:
            raise ValueError("Gültigkeitsmonate müssen zwischen 1 und 255 liegen")
            
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 65535:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs")
        
        # Erstelle Eingabedaten für HMAC
        input_data = struct.pack('<H B', days_since_ref, validity_months)
        
        # Berechne HMAC mit device_key
        mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
        
        # Verwende erste 4 Bytes des HMAC
        mac_int = struct.unpack('<I', mac[:4])[0]
        
        # Kombiniere mit Eingabedaten für eindeutige PIN
        combined = (mac_int ^ (days_since_ref << 16) ^ (validity_months << 8)) & 0xFFFFFFFF
        
        # Auf 8 Stellen normalisieren
        pin = str(combined % 100000000).zfill(8)
        
        return pin
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        
        Returns:
            8-stelliger PIN für unbegrenzte Nutzung
        """
        # Spezielle Werte für unbegrenzte Lizenz
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, 255)


class TimedPinValidator:
    """
    Validator für zeitbasierte PINs
    """
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert einen PIN durch Brute-Force über mögliche Eingabeparameter
        
        Args:
            pin: 8-stelliger PIN
            current_date: Aktuelles Datum (Standard: heute)
            
        Returns:
            Dictionary mit Validierungsergebnis und Lizenzinformationen
        """
        if current_date is None:
            current_date = datetime.now()
            
        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }
        
        pin_int = int(pin)
        ref_date = datetime(2020, 1, 1)
        
        # Brute-Force über realistische Datumsbereiche und Gültigkeitsmonate
        # Optimiert für Performance: nur wahrscheinliche Werte testen
        
        # Optimierte Suche: Teste zuerst spezielle Werte für unbegrenzte Lizenzen
        special_date = datetime(2099, 12, 31)
        special_days = (special_date.date() - ref_date.date()).days

        # Teste unbegrenzte Lizenz zuerst
        try:
            input_data = struct.pack('<H B', special_days, 255)
            mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
            mac_int = struct.unpack('<I', mac[:4])[0]
            combined = (mac_int ^ (special_days << 16) ^ (255 << 8)) & 0xFFFFFFFF
            expected_pin = str(combined % 100000000).zfill(8)

            if expected_pin == pin:
                return {
                    'valid': True,
                    'unlimited': True,
                    'purchase_date': special_date,
                    'validity_months': 255,
                    'expires': None
                }
        except (struct.error, ValueError):
            pass

        # Teste normale Datumsbereich: 2020-2030 (ca. 3650 Tage)
        for days_since_ref in range(0, 3650, 1):  # Jeden Tag testen
            for validity_months in [1, 3, 6, 12, 18, 24, 36, 48, 60]:  # Häufige Werte (ohne 255)
                try:
                    # Berechne erwarteten PIN für diese Parameter
                    input_data = struct.pack('<H B', days_since_ref, validity_months)
                    mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
                    mac_int = struct.unpack('<I', mac[:4])[0]
                    combined = (mac_int ^ (days_since_ref << 16) ^ (validity_months << 8)) & 0xFFFFFFFF
                    expected_pin = str(combined % 100000000).zfill(8)

                    if expected_pin == pin:
                        # PIN gefunden! Berechne Lizenzinformationen
                        purchase_date = ref_date + timedelta(days=days_since_ref)

                        # Berechne Ablaufdatum
                        expiry_date = purchase_date + timedelta(days=validity_months * 30)
                        is_valid = current_date <= expiry_date

                        return {
                            'valid': is_valid,
                            'unlimited': False,
                            'purchase_date': purchase_date,
                            'validity_months': validity_months,
                            'expires': expiry_date,
                            'days_remaining': (expiry_date - current_date).days if is_valid else 0
                        }

                except (struct.error, ValueError):
                    continue
        
        # Erweiterte Suche für andere Gültigkeitsmonate (reduziert für Performance)
        for days_since_ref in range(0, 29220, 30):  # Jeden Monat testen
            for validity_months in range(1, 256):  # Alle möglichen Monate
                try:
                    input_data = struct.pack('<H B', days_since_ref, validity_months)
                    mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
                    mac_int = struct.unpack('<I', mac[:4])[0]
                    combined = (mac_int ^ (days_since_ref << 16) ^ (validity_months << 8)) & 0xFFFFFFFF
                    expected_pin = str(combined % 100000000).zfill(8)
                    
                    if expected_pin == pin:
                        purchase_date = ref_date + timedelta(days=days_since_ref)
                        
                        if validity_months == 255 and purchase_date.year == 2099:
                            return {
                                'valid': True,
                                'unlimited': True,
                                'purchase_date': purchase_date,
                                'validity_months': validity_months,
                                'expires': None
                            }
                        
                        expiry_date = purchase_date + timedelta(days=validity_months * 30)
                        is_valid = current_date <= expiry_date
                        
                        return {
                            'valid': is_valid,
                            'unlimited': False,
                            'purchase_date': purchase_date,
                            'validity_months': validity_months,
                            'expires': expiry_date,
                            'days_remaining': (expiry_date - current_date).days if is_valid else 0
                        }
                        
                except (struct.error, ValueError):
                    continue
        
        return {
            'valid': False,
            'error': 'PIN konnte nicht validiert werden'
        }


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 32-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(32)


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = TimedPinGenerator(device_key)
    validator = TimedPinValidator(device_key)
    
    # PIN für 12 Monate ab heute generieren
    purchase_date = datetime.now()
    pin = generator.generate_pin(purchase_date, 12)
    print(f"Generierter PIN: {pin}")
    
    # PIN validieren
    result = validator.validate_pin(pin)
    print(f"Validierungsergebnis: {result}")
    
    # Unbegrenzten PIN generieren
    unlimited_pin = generator.generate_unlimited_pin()
    print(f"Unbegrenzter PIN: {unlimited_pin}")
    
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzte Validierung: {unlimited_result}")
    
    # Test mit spezifischem Datum
    test_date = datetime(2024, 6, 15)
    test_pin = generator.generate_pin(test_date, 6)
    print(f"\nTest PIN für {test_date.strftime('%d.%m.%Y')}, 6 Monate: {test_pin}")
    
    test_result = validator.validate_pin(test_pin, test_date)
    print(f"Test Validierung: {test_result}")
