#!/usr/bin/env python3
"""
Ultra-optimiertes zeitbasiertes PIN-System für Embedded Systeme
Beschränkt auf nur 3 Gültigkeitswerte: 12, 24, 36 Monate für maximale Performance
"""

import struct
import hashlib
import hmac
from datetime import datetime, timedelta
import os
import time


class UltraOptimizedPinGenerator:
    """
    Ultra-optimierter PIN-Generator für Embedded Systeme
    Nur 3 erlaubte Gültigkeitswerte: 12, 24, 36 Monate
    """
    
    # Konstanten für Ultra-Optimierung
    VALID_MONTHS = [12, 24, 36]  # Nur diese 3 Werte erlaubt
    UNLIMITED_MONTHS = 255       # Spezialwert für unbegrenzte Lizenz
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Generator mit gerätespezifischem Schlüssel
        
        Args:
            device_key: 32-Byte gerätespezifischer Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def generate_pin(self, purchase_date: datetime, validity_months: int) -> str:
        """
        Generiert einen 8-stelligen PIN für gegebenes Kaufdatum und Gültigkeitsdauer
        
        Args:
            purchase_date: Kaufdatum
            validity_months: Gültigkeitsdauer (12, 24, 36 oder 255 für unbegrenzt)
            
        Returns:
            8-stelliger PIN als String
            
        Raises:
            ValueError: Bei ungültigen Parametern
        """
        # Strenge Validierung der Eingabeparameter
        if validity_months != self.UNLIMITED_MONTHS and validity_months not in self.VALID_MONTHS:
            raise ValueError(f"Gültigkeitsmonate müssen einer der Werte sein: {self.VALID_MONTHS} (oder 255 für unbegrenzt)")
            
        # Referenzdatum: 1.1.2020
        ref_date = datetime(2020, 1, 1)
        days_since_ref = (purchase_date.date() - ref_date.date()).days
        
        if days_since_ref < 0 or days_since_ref > 65535:
            raise ValueError("Kaufdatum außerhalb des gültigen Bereichs (2020-2099)")
        
        # Erstelle Eingabedaten für HMAC (3 Bytes)
        input_data = struct.pack('<H B', days_since_ref, validity_months)
        
        # Berechne HMAC mit device_key
        mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
        
        # Verwende erste 4 Bytes des HMAC
        mac_int = struct.unpack('<I', mac[:4])[0]
        
        # Kombiniere mit Eingabedaten für eindeutige PIN
        combined = (mac_int ^ (days_since_ref << 16) ^ (validity_months << 8)) & 0xFFFFFFFF
        
        # Auf 8 Stellen normalisieren
        pin = str(combined % 100000000).zfill(8)
        
        return pin
    
    def generate_unlimited_pin(self) -> str:
        """
        Generiert einen unbegrenzten PIN für interne Zwecke
        
        Returns:
            8-stelliger PIN für unbegrenzte Nutzung
        """
        special_date = datetime(2099, 12, 31)
        return self.generate_pin(special_date, self.UNLIMITED_MONTHS)


class UltraOptimizedPinValidator:
    """
    Ultra-optimierter PIN-Validator für Embedded Systeme
    Nur 3 Gültigkeitswerte: 12, 24, 36 Monate
    """
    
    # Konstanten (müssen mit Generator übereinstimmen)
    VALID_MONTHS = [12, 24, 36]
    UNLIMITED_MONTHS = 255
    
    def __init__(self, device_key: bytes):
        """
        Initialisiert den Validator mit gerätespezifischem Schlüssel
        """
        if len(device_key) != 32:
            raise ValueError("Device key muss genau 32 Bytes lang sein")
        self.device_key = device_key
        
    def validate_pin(self, pin: str, current_date: datetime = None) -> dict:
        """
        Validiert einen PIN mit ultra-optimierter Suche
        
        ULTRA-OPTIMIERUNG: Nur 3 × 3650 = 10.950 Berechnungen statt 131.400!
        Das ist eine 92% Reduzierung gegenüber der 36-Monate Version!
        
        Args:
            pin: 8-stelliger PIN
            current_date: Aktuelles Datum (Standard: heute)
            
        Returns:
            Dictionary mit Validierungsergebnis und Lizenzinformationen
        """
        if current_date is None:
            current_date = datetime.now()
            
        if len(pin) != 8 or not pin.isdigit():
            return {
                'valid': False,
                'error': 'PIN muss 8 Ziffern enthalten'
            }
        
        pin_int = int(pin)
        ref_date = datetime(2020, 1, 1)
        
        # 1. Teste unbegrenzte Lizenz zuerst (1 Berechnung)
        special_days = (datetime(2099, 12, 31).date() - ref_date.date()).days
        if self._test_combination(pin_int, special_days, self.UNLIMITED_MONTHS):
            return {
                'valid': True,
                'unlimited': True,
                'purchase_date': datetime(2099, 12, 31),
                'validity_months': self.UNLIMITED_MONTHS,
                'expires': None
            }
        
        # 2. ULTRA-OPTIMIERTE SUCHE: Nur 3 Gültigkeitswerte!
        # Reduzierung: 3650 Tage × 3 Monate = 10.950 Berechnungen
        # Statt 3650 × 36 = 131.400 (92% weniger!)
        
        for days_since_ref in range(0, 3650, 1):  # 2020-2030 (10 Jahre)
            for validity_months in self.VALID_MONTHS:  # Nur [12, 24, 36]
                if self._test_combination(pin_int, days_since_ref, validity_months):
                    purchase_date = ref_date + timedelta(days=days_since_ref)
                    expiry_date = purchase_date + timedelta(days=validity_months * 30)
                    is_valid = current_date <= expiry_date
                    
                    return {
                        'valid': is_valid,
                        'unlimited': False,
                        'purchase_date': purchase_date,
                        'validity_months': validity_months,
                        'expires': expiry_date,
                        'days_remaining': (expiry_date - current_date).days if is_valid else 0
                    }
        
        return {
            'valid': False,
            'error': 'PIN konnte nicht validiert werden'
        }
    
    def _test_combination(self, target_pin: int, days: int, months: int) -> bool:
        """
        Testet eine spezifische Kombination von Tagen und Monaten
        Optimiert für minimale Berechnungen
        """
        try:
            # Erstelle Eingabedaten
            input_data = struct.pack('<H B', days, months)
            
            # Berechne HMAC
            mac = hmac.new(self.device_key, input_data, hashlib.sha256).digest()
            mac_int = struct.unpack('<I', mac[:4])[0]
            
            # Kombiniere für PIN
            combined = (mac_int ^ (days << 16) ^ (months << 8)) & 0xFFFFFFFF
            calculated_pin = combined % 100000000
            
            return calculated_pin == target_pin
            
        except (struct.error, ValueError):
            return False


def generate_device_key() -> bytes:
    """
    Generiert einen neuen 32-Byte gerätespezifischen Schlüssel
    """
    return os.urandom(32)


# Ultra-Performance-Benchmarking
def ultra_benchmark_validation(device_key: bytes, num_tests: int = 100):
    """
    Benchmarkt die ultra-optimierte Validierungsperformance
    """
    generator = UltraOptimizedPinGenerator(device_key)
    validator = UltraOptimizedPinValidator(device_key)
    
    # Generiere Test-PINs für alle 3 erlaubten Gültigkeiten
    test_pins = []
    test_date = datetime(2024, 6, 15)
    
    for months in UltraOptimizedPinValidator.VALID_MONTHS:
        pin = generator.generate_pin(test_date, months)
        test_pins.append((pin, months))
    
    # Füge unbegrenzten PIN hinzu
    unlimited_pin = generator.generate_unlimited_pin()
    test_pins.append((unlimited_pin, 255))
    
    # Benchmark
    print(f"ULTRA-OPTIMIERTES PIN-System Benchmark (nur 3 Gültigkeitswerte):")
    print("=" * 80)
    print(f"Suchraum-Reduzierung: 10.950 statt 131.400 Berechnungen (92% weniger!)")
    print("=" * 80)
    
    total_time = 0
    total_validations = 0
    worst_case_time = 0
    best_case_time = float('inf')
    
    for pin, months in test_pins:
        times = []
        
        for _ in range(num_tests):
            start_time = time.time()
            result = validator.validate_pin(pin, test_date)
            end_time = time.time()
            
            assert result['valid'], f"PIN {pin} sollte gültig sein"
            
            validation_time = (end_time - start_time) * 1000  # in ms
            times.append(validation_time)
        
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        total_time += sum(times)
        total_validations += num_tests
        
        worst_case_time = max(worst_case_time, max_time)
        best_case_time = min(best_case_time, min_time)
        
        validity_str = "Unbegrenzt" if months == 255 else f"{months} Monate"
        print(f"• {validity_str:12} - PIN: {pin} - Ø{avg_time:.1f}ms (min:{min_time:.1f}ms, max:{max_time:.1f}ms)")
    
    overall_avg = total_time / total_validations
    
    print(f"\n" + "=" * 80)
    print(f"PERFORMANCE-ERGEBNISSE:")
    print(f"• Durchschnitt: {overall_avg:.1f}ms pro Validierung")
    print(f"• Bester Fall:  {best_case_time:.1f}ms")
    print(f"• Schlechtester Fall: {worst_case_time:.1f}ms")
    print(f"• Geschätzte CPU-Zyklen bei 100MHz: ~{overall_avg * 100:.0f}k Zyklen")
    
    # Vergleich mit vorherigen Versionen
    print(f"\n" + "=" * 80)
    print(f"OPTIMIERUNGS-VERGLEICH:")
    print(f"• Original (255 Monate):     930.750 Berechnungen")
    print(f"• Optimiert (36 Monate):     131.400 Berechnungen (-86%)")
    print(f"• ULTRA-OPTIMIERT (3 Werte): 10.950 Berechnungen (-92% vs. optimiert, -99% vs. original!)")
    
    # Hochrechnung für 100MHz CPU
    cycles_per_validation = overall_avg * 100 * 1000  # ms * 100MHz
    print(f"\n" + "=" * 80)
    print(f"100MHz CPU ANALYSE:")
    print(f"• Durchschnittliche Zyklen: {cycles_per_validation:,.0f}")
    print(f"• Maximale Zyklen: {worst_case_time * 100 * 1000:,.0f}")
    print(f"• Validierungen pro Sekunde: {1000 / overall_avg:.0f}")
    
    if overall_avg <= 50:  # Ziel: unter 50ms
        print(f"✅ ZIEL ERREICHT: Validierung unter 50ms für 100MHz CPU!")
    else:
        print(f"⚠️  Ziel verfehlt: {overall_avg:.1f}ms > 50ms")
    
    return overall_avg


def cpu_cycle_estimation(avg_time_ms: float, cpu_freq_mhz: int = 100):
    """
    Schätzt CPU-Zyklen basierend auf gemessener Zeit
    """
    cycles = avg_time_ms * cpu_freq_mhz * 1000
    
    print(f"\nCPU-ZYKLEN SCHÄTZUNG für {cpu_freq_mhz}MHz:")
    print(f"• Validierungszeit: {avg_time_ms:.1f}ms")
    print(f"• Geschätzte Zyklen: {cycles:,.0f}")
    print(f"• Zyklen pro HMAC: {cycles / 10950:.0f} (bei worst-case)")
    
    # Vergleich mit Hardware-Limits
    if cycles < 5_000_000:  # 5M Zyklen = 50ms bei 100MHz
        print(f"✅ EXCELLENT: Sehr effizient für {cpu_freq_mhz}MHz CPU")
    elif cycles < 10_000_000:  # 10M Zyklen = 100ms
        print(f"✅ GOOD: Akzeptabel für {cpu_freq_mhz}MHz CPU")
    elif cycles < 50_000_000:  # 50M Zyklen = 500ms
        print(f"⚠️  OK: Grenzwertig für {cpu_freq_mhz}MHz CPU")
    else:
        print(f"❌ POOR: Zu langsam für {cpu_freq_mhz}MHz CPU")
    
    return cycles


if __name__ == "__main__":
    # Beispiel-Verwendung
    device_key = generate_device_key()
    
    generator = UltraOptimizedPinGenerator(device_key)
    validator = UltraOptimizedPinValidator(device_key)
    
    print("ULTRA-OPTIMIERTES PIN-System für Embedded Systeme")
    print("=" * 60)
    print("Erlaubte Gültigkeitswerte: 12, 24, 36 Monate")
    print("=" * 60)
    
    # Test aller erlaubten Werte
    purchase_date = datetime.now()
    
    for months in UltraOptimizedPinValidator.VALID_MONTHS:
        pin = generator.generate_pin(purchase_date, months)
        result = validator.validate_pin(pin)
        print(f"PIN für {months:2d} Monate: {pin} - Gültig: {result['valid']}")
    
    # Unbegrenzter PIN
    unlimited_pin = generator.generate_unlimited_pin()
    unlimited_result = validator.validate_pin(unlimited_pin)
    print(f"Unbegrenzter PIN:      {unlimited_pin} - Gültig: {unlimited_result['valid']}")
    
    # Test ungültiger Werte
    print(f"\n" + "=" * 60)
    print("Test ungültiger Gültigkeitswerte:")
    
    for invalid_months in [1, 6, 15, 18, 30]:
        try:
            generator.generate_pin(purchase_date, invalid_months)
            print(f"❌ FEHLER: {invalid_months} Monate sollten nicht erlaubt sein!")
        except ValueError as e:
            print(f"✅ Korrekt abgelehnt: {invalid_months} Monate")
    
    # Ultra-Performance-Test
    print(f"\n" + "=" * 80)
    avg_time = ultra_benchmark_validation(device_key, 50)
    
    # CPU-Zyklen Analyse
    cpu_cycle_estimation(avg_time, 100)
