import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
from Crypto.Cipher import AES

# Geräteindividueller 32-Byte-Schlüssel

DEVICE_KEY = b'ThisIsA32ByteDeviceSpecificKey123'  # 32 Bytes


# CRC8-Funktion
def crc8(data: bytes):
    crc = 0
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x80:
                crc = (crc << 1) ^ 0x07
            else:
                crc <<= 1
            crc &= 0xFF
    return crc

# Klartextdatenstruktur: 17 Bit Datum, 6 Bit Gültigkeit, 9 Bit CRC8
def build_data_block(purchase_date: datetime, validity_months: int, device_key: bytes):
    days_since_2020 = (purchase_date - datetime(2020, 1, 1)).days
    if days_since_2020 >= 2**17:
        raise ValueError("Kaufdatum zu weit in der Zukunft.")
    if not (0 < validity_months <= 36):
        raise ValueError("Gültigkeit muss zwischen 1 und 36 Monaten liegen.")
    crc = crc8(device_key)
    value = (days_since_2020 << 15) | (validity_months << 9) | crc
    return value.to_bytes(4, 'big'), days_since_2020, validity_months, crc

# Verschlüsselung → PIN
def generate_pin(purchase_date: datetime, validity_months: int, device_key: bytes):
    data_block, days, validity, crc = build_data_block(purchase_date, validity_months, device_key)
    cipher = AES.new(device_key, AES.MODE_ECB)
    encrypted = cipher.encrypt(data_block.ljust(16, b'\x00'))
    pin_number = int.from_bytes(encrypted[:4], 'big') % 100_000_000
    return f"{pin_number:08d}", days, validity, crc

# Verifikation: brute-force über begrenzten Bereich
def verify_pin(pin: str, device_key: bytes):
    target = int(pin)
    cipher = AES.new(device_key, AES.MODE_ECB)
    crc = crc8(device_key)
    max_days = 365 * 20  # 20 Jahre

    for days in range(max_days):
        for validity in range(1, 37):
            value = (days << 15) | (validity << 9) | crc
            data_block = value.to_bytes(4, 'big')
            encrypted = cipher.encrypt(data_block.ljust(16, b'\x00'))
            candidate = int.from_bytes(encrypted[:4], 'big') % 100_000_000
            if candidate == target:
                purchase_date = datetime(2020, 1, 1) + timedelta(days=days)
                expiry_date = purchase_date + timedelta(days=validity * 30)
                now = datetime.now().date()
                return {
                    "purchase_date": purchase_date.date(),
                    "valid_until": expiry_date.date(),
                    "valid": now <= expiry_date.date(),
                    "validity_months": validity,
                    "crc": crc,
                    "device_binding_ok": True
                }
    return {"error": "PIN ungültig oder nicht von diesem Gerät."}

# GUI
class PinApp:
    def __init__(self, root):
        self.root = root
        self.root.title("PIN Generator & Verifier")

        tab_control = ttk.Notebook(root)
        self.gen_tab = ttk.Frame(tab_control)
        self.ver_tab = ttk.Frame(tab_control)

        tab_control.add(self.gen_tab, text='PIN generieren')
        tab_control.add(self.ver_tab, text='PIN verifizieren')
        tab_control.pack(expand=1, fill='both')

        self.build_generate_tab()
        self.build_verify_tab()

    def build_generate_tab(self):
        ttk.Label(self.gen_tab, text="Kaufdatum (YYYY-MM-DD):").grid(column=0, row=0, padx=10, pady=5, sticky='w')
        self.date_entry = ttk.Entry(self.gen_tab)
        self.date_entry.grid(column=1, row=0, padx=10, pady=5)

        ttk.Label(self.gen_tab, text="Gültigkeit (Monate, max 36):").grid(column=0, row=1, padx=10, pady=5, sticky='w')
        self.validity_entry = ttk.Entry(self.gen_tab)
        self.validity_entry.grid(column=1, row=1, padx=10, pady=5)

        self.pin_output = tk.StringVar()
        self.details_output = tk.StringVar()

        ttk.Button(self.gen_tab, text="PIN generieren", command=self.generate_pin_action).grid(column=0, row=2, columnspan=2, pady=10)
        ttk.Label(self.gen_tab, textvariable=self.pin_output, font=('Courier', 14)).grid(column=0, row=3, columnspan=2, pady=5)
        ttk.Label(self.gen_tab, textvariable=self.details_output, font=('Courier', 10), justify='left').grid(column=0, row=4, columnspan=2, pady=5)

    def build_verify_tab(self):
        ttk.Label(self.ver_tab, text="PIN eingeben (8 Ziffern):").grid(column=0, row=0, padx=10, pady=5, sticky='w')
        self.pin_entry = ttk.Entry(self.ver_tab)
        self.pin_entry.grid(column=1, row=0, padx=10, pady=5)

        self.verify_output = tk.StringVar()
        ttk.Button(self.ver_tab, text="PIN verifizieren", command=self.verify_pin_action).grid(column=0, row=1, columnspan=2, pady=10)
        ttk.Label(self.ver_tab, textvariable=self.verify_output, font=('Courier', 10), justify='left').grid(column=0, row=2, columnspan=2, pady=5)

    def generate_pin_action(self):
        try:
            date_str = self.date_entry.get()
            validity = int(self.validity_entry.get())
            purchase_date = datetime.strptime(date_str, "%Y-%m-%d")
            pin, days, validity, crc = generate_pin(purchase_date, validity, DEVICE_KEY)
            self.pin_output.set(f"PIN: {pin}")
            self.details_output.set(f"Kaufdatum: {purchase_date.date()}\nGültigkeit: {validity} Monate\nCRC8: {crc:02X}")
        except Exception as e:
            messagebox.showerror("Fehler", str(e))

    def verify_pin_action(self):
        pin = self.pin_entry.get().strip()
        result = verify_pin(pin, DEVICE_KEY)
        if "error" in result:
            self.verify_output.set(f"Fehler: {result['error']}")
        else:
            output = (
                f"Kaufdatum: {result['purchase_date']}\n"
                f"Gültigkeit: {result['validity_months']} Monate\n"
                f"CRC8: {result['crc']:02X}\n"
                f"Gültig bis: {result['valid_until']}\n"
                f"Gültig: {'Ja' if result['valid'] else 'Nein'}\n"
                f"Gerätebindung: {'OK' if result['device_binding_ok'] else 'Fehler'}"
            )
            self.verify_output.set(output)

# Start
if __name__ == "__main__":
    root = tk.Tk()
    app = PinApp(root)
    root.mainloop()
