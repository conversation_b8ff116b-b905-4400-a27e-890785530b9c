#!/usr/bin/env python3
"""
Erweiterte PIN-Generator GUI
Mit Batch-Generierung, Export-Funktionen und erweiterten Features
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
import os
import binascii
import csv
from pin_generator_final import TimedPinGenerator, TimedPinValidator, generate_device_key


class AdvancedPinGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Erweiterte PIN-Generator Suite")
        self.root.geometry("900x900")
        self.root.resizable(True, True)
        
        # Variablen
        self.device_key = None
        self.generator = None
        self.validator = None
        self.batch_results = []
        
        self.setup_ui()
        self.load_default_key()
        
    def setup_ui(self):
        """Erstellt die erweiterte Benutzeroberfläche"""
        
        # Notebook für Tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Einzelne PIN-Generierung
        self.single_frame = ttk.Frame(notebook)
        notebook.add(self.single_frame, text="Einzelne PIN-Generierung")
        self.setup_single_pin_tab()
        
        # Tab 2: Batch-Generierung
        self.batch_frame = ttk.Frame(notebook)
        notebook.add(self.batch_frame, text="Batch-Generierung")
        self.setup_batch_tab()
        
        # Tab 3: PIN-Validierung
        self.validation_frame = ttk.Frame(notebook)
        notebook.add(self.validation_frame, text="PIN-Validierung")
        self.setup_validation_tab()
        
        # Tab 4: Device-Key Management
        self.key_frame = ttk.Frame(notebook)
        notebook.add(self.key_frame, text="Device-Key Management")
        self.setup_key_management_tab()
        
        # Status Bar
        self.status_var = tk.StringVar(value="Bereit")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
        
    def setup_single_pin_tab(self):
        """Tab für einzelne PIN-Generierung"""
        main_frame = ttk.Frame(self.single_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titel
        ttk.Label(main_frame, text="Einzelne PIN-Generierung", 
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # Eingabebereich
        input_frame = ttk.LabelFrame(main_frame, text="Parameter", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Datum
        date_frame = ttk.Frame(input_frame)
        date_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(date_frame, text="Kaufdatum:", width=15).pack(side=tk.LEFT)
        
        self.single_day_var = tk.StringVar(value=str(datetime.now().day))
        self.single_month_var = tk.StringVar(value=str(datetime.now().month))
        self.single_year_var = tk.StringVar(value=str(datetime.now().year))
        
        ttk.Entry(date_frame, textvariable=self.single_day_var, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.single_month_var, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.single_year_var, width=5).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(date_frame, text="Heute", 
                  command=self.set_single_today).pack(side=tk.LEFT, padx=(10, 0))
        
        # Gültigkeit
        validity_frame = ttk.Frame(input_frame)
        validity_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(validity_frame, text="Gültigkeit (Monate):", width=15).pack(side=tk.LEFT)
        self.single_validity_var = tk.StringVar(value="12")
        ttk.Entry(validity_frame, textvariable=self.single_validity_var, width=10).pack(side=tk.LEFT, padx=2)
        
        # Schnellauswahl
        for months in [1, 3, 6, 12, 24, 36]:
            ttk.Button(validity_frame, text=f"{months}M", width=4,
                      command=lambda m=months: self.single_validity_var.set(str(m))).pack(side=tk.LEFT, padx=1)
        
        ttk.Button(validity_frame, text="∞", width=4,
                  command=self.set_single_unlimited).pack(side=tk.LEFT, padx=1)
        
        # Generieren Button
        ttk.Button(input_frame, text="PIN Generieren", 
                  command=self.generate_single_pin).pack(pady=10)
        
        # Ergebnis
        result_frame = ttk.LabelFrame(main_frame, text="Ergebnis", padding="10")
        result_frame.pack(fill=tk.X, pady=(0, 10))
        
        pin_frame = ttk.Frame(result_frame)
        pin_frame.pack(fill=tk.X)
        
        ttk.Label(pin_frame, text="Generierter PIN:", width=15).pack(side=tk.LEFT)
        self.single_pin_var = tk.StringVar()
        pin_entry = ttk.Entry(pin_frame, textvariable=self.single_pin_var, 
                             font=("Courier", 14, "bold"), state="readonly", width=10)
        pin_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(pin_frame, text="Kopieren", 
                  command=self.copy_single_pin).pack(side=tk.LEFT, padx=5)
        
        # Info Text
        self.single_info_text = tk.Text(result_frame, height=5, wrap=tk.WORD)
        self.single_info_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
    def setup_batch_tab(self):
        """Tab für Batch-Generierung"""
        main_frame = ttk.Frame(self.batch_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titel
        ttk.Label(main_frame, text="Batch PIN-Generierung", 
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # Parameter
        param_frame = ttk.LabelFrame(main_frame, text="Batch-Parameter", padding="10")
        param_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Startdatum
        start_frame = ttk.Frame(param_frame)
        start_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(start_frame, text="Startdatum:", width=15).pack(side=tk.LEFT)
        
        self.batch_start_day = tk.StringVar(value=str(datetime.now().day))
        self.batch_start_month = tk.StringVar(value=str(datetime.now().month))
        self.batch_start_year = tk.StringVar(value=str(datetime.now().year))
        
        ttk.Entry(start_frame, textvariable=self.batch_start_day, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(start_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(start_frame, textvariable=self.batch_start_month, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(start_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(start_frame, textvariable=self.batch_start_year, width=5).pack(side=tk.LEFT, padx=2)
        
        # Anzahl PINs
        count_frame = ttk.Frame(param_frame)
        count_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(count_frame, text="Anzahl PINs:", width=15).pack(side=tk.LEFT)
        self.batch_count_var = tk.StringVar(value="10")
        ttk.Entry(count_frame, textvariable=self.batch_count_var, width=10).pack(side=tk.LEFT, padx=2)
        
        # Gültigkeit
        batch_validity_frame = ttk.Frame(param_frame)
        batch_validity_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(batch_validity_frame, text="Gültigkeit (Monate):", width=15).pack(side=tk.LEFT)
        self.batch_validity_var = tk.StringVar(value="12")
        ttk.Entry(batch_validity_frame, textvariable=self.batch_validity_var, width=10).pack(side=tk.LEFT, padx=2)
        
        # Intervall
        interval_frame = ttk.Frame(param_frame)
        interval_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(interval_frame, text="Intervall (Tage):", width=15).pack(side=tk.LEFT)
        self.batch_interval_var = tk.StringVar(value="1")
        ttk.Entry(interval_frame, textvariable=self.batch_interval_var, width=10).pack(side=tk.LEFT, padx=2)
        
        # Buttons
        button_frame = ttk.Frame(param_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Batch Generieren", 
                  command=self.generate_batch_pins).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Als CSV Exportieren", 
                  command=self.export_batch_csv).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Löschen", 
                  command=self.clear_batch).pack(side=tk.LEFT, padx=5)
        
        # Ergebnisse
        result_frame = ttk.LabelFrame(main_frame, text="Batch-Ergebnisse", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # Treeview für Ergebnisse
        columns = ("Nr", "Datum", "Gültigkeit", "PIN", "Ablauf")
        self.batch_tree = ttk.Treeview(result_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.batch_tree.heading(col, text=col)
            self.batch_tree.column(col, width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
        h_scrollbar = ttk.Scrollbar(result_frame, orient=tk.HORIZONTAL, command=self.batch_tree.xview)
        
        self.batch_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        self.batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def setup_validation_tab(self):
        """Tab für PIN-Validierung"""
        main_frame = ttk.Frame(self.validation_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titel
        ttk.Label(main_frame, text="PIN-Validierung", 
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # Eingabe
        input_frame = ttk.LabelFrame(main_frame, text="PIN-Eingabe", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        # PIN
        pin_frame = ttk.Frame(input_frame)
        pin_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(pin_frame, text="PIN:", width=15).pack(side=tk.LEFT)
        self.val_pin_var = tk.StringVar()
        self.val_pin_entry = ttk.Entry(pin_frame, textvariable=self.val_pin_var, 
                                      font=("Courier", 12), width=10)
        self.val_pin_entry.pack(side=tk.LEFT, padx=5)
        self.val_pin_entry.bind('<Return>', lambda e: self.validate_pin())
        
        ttk.Button(pin_frame, text="Einfügen", 
                  command=self.paste_validation_pin).pack(side=tk.LEFT, padx=5)
        
        # Prüfdatum
        date_frame = ttk.Frame(input_frame)
        date_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(date_frame, text="Prüfdatum:", width=15).pack(side=tk.LEFT)
        
        self.val_day_var = tk.StringVar(value=str(datetime.now().day))
        self.val_month_var = tk.StringVar(value=str(datetime.now().month))
        self.val_year_var = tk.StringVar(value=str(datetime.now().year))
        
        ttk.Entry(date_frame, textvariable=self.val_day_var, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.val_month_var, width=3).pack(side=tk.LEFT, padx=2)
        ttk.Label(date_frame, text=".").pack(side=tk.LEFT)
        ttk.Entry(date_frame, textvariable=self.val_year_var, width=5).pack(side=tk.LEFT, padx=2)
        
        ttk.Button(date_frame, text="Heute", 
                  command=self.set_validation_today).pack(side=tk.LEFT, padx=(10, 0))
        
        # Validieren Button
        ttk.Button(input_frame, text="PIN Validieren", 
                  command=self.validate_pin).pack(pady=10)
        
        # Ergebnis
        result_frame = ttk.LabelFrame(main_frame, text="Validierungsergebnis", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.validation_result_text = tk.Text(result_frame, wrap=tk.WORD)
        val_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.validation_result_text.yview)
        
        self.validation_result_text.configure(yscrollcommand=val_scrollbar.set)
        self.validation_result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        val_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def setup_key_management_tab(self):
        """Tab für Device-Key Management"""
        main_frame = ttk.Frame(self.key_frame, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titel
        ttk.Label(main_frame, text="Device-Key Management", 
                 font=("Arial", 14, "bold")).pack(pady=(0, 20))
        
        # Aktueller Key
        current_frame = ttk.LabelFrame(main_frame, text="Aktueller Device-Key", padding="10")
        current_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Key Anzeige
        key_display_frame = ttk.Frame(current_frame)
        key_display_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(key_display_frame, text="Hex Key:").pack(anchor=tk.W)
        self.key_display_var = tk.StringVar()
        key_display_entry = ttk.Entry(key_display_frame, textvariable=self.key_display_var, 
                                     state="readonly", width=70)
        key_display_entry.pack(fill=tk.X, pady=2)
        
        # Key Info
        self.key_info_display_var = tk.StringVar(value="Kein Key geladen")
        ttk.Label(current_frame, textvariable=self.key_info_display_var, 
                 foreground="gray").pack(anchor=tk.W, pady=2)
        
        # Key Aktionen
        action_frame = ttk.LabelFrame(main_frame, text="Key-Aktionen", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Button Grid
        button_grid = ttk.Frame(action_frame)
        button_grid.pack()
        
        ttk.Button(button_grid, text="Neuen Key generieren", 
                  command=self.generate_new_key).grid(row=0, column=0, padx=5, pady=2, sticky=tk.W)
        ttk.Button(button_grid, text="Key aus Datei laden", 
                  command=self.load_key_file).grid(row=0, column=1, padx=5, pady=2, sticky=tk.W)
        ttk.Button(button_grid, text="Key in Datei speichern", 
                  command=self.save_key_file).grid(row=1, column=0, padx=5, pady=2, sticky=tk.W)
        ttk.Button(button_grid, text="Key von Hex eingeben", 
                  command=self.input_hex_key).grid(row=1, column=1, padx=5, pady=2, sticky=tk.W)
        
        # Key Eingabe
        input_frame = ttk.LabelFrame(main_frame, text="Key manuell eingeben", padding="10")
        input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_frame, text="Hex-String (64 Zeichen):").pack(anchor=tk.W)
        self.manual_key_var = tk.StringVar()
        manual_key_entry = ttk.Entry(input_frame, textvariable=self.manual_key_var, width=70)
        manual_key_entry.pack(fill=tk.X, pady=2)
        
        ttk.Button(input_frame, text="Key übernehmen", 
                  command=self.apply_manual_key).pack(pady=5)
        
        # Key Historie (falls gewünscht)
        history_frame = ttk.LabelFrame(main_frame, text="Informationen", padding="10")
        history_frame.pack(fill=tk.BOTH, expand=True)
        
        info_text = tk.Text(history_frame, height=8, wrap=tk.WORD)
        info_text.pack(fill=tk.BOTH, expand=True)
        
        info_text.insert(tk.END, "Device-Key Informationen:\n\n")
        info_text.insert(tk.END, "• Der Device-Key ist 32 Bytes (256 Bit) lang\n")
        info_text.insert(tk.END, "• Jedes Gerät sollte einen einzigartigen Key haben\n")
        info_text.insert(tk.END, "• Der Key wird für die PIN-Generierung und -Validierung verwendet\n")
        info_text.insert(tk.END, "• Bewahren Sie den Key sicher auf - ohne ihn können PINs nicht validiert werden\n")
        info_text.insert(tk.END, "• Der Key wird automatisch als 'default_device_key.bin' gespeichert\n")
        info_text.configure(state=tk.DISABLED)

    def load_default_key(self):
        """Lädt einen Standard-Key oder generiert einen neuen"""
        try:
            if os.path.exists("default_device_key.bin"):
                with open("default_device_key.bin", "rb") as f:
                    key_data = f.read()
                    if len(key_data) == 32:
                        self.set_device_key(key_data)
                        self.status_var.set("Standard Device-Key geladen")
                        return

            self.generate_new_key()

        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden des Standard-Keys: {e}")

    def set_device_key(self, key_bytes):
        """Setzt den Device-Key und aktualisiert alle GUI-Elemente"""
        try:
            if len(key_bytes) != 32:
                raise ValueError("Device-Key muss genau 32 Bytes lang sein")

            self.device_key = key_bytes
            self.generator = TimedPinGenerator(key_bytes)
            self.validator = TimedPinValidator(key_bytes)

            # Zeige Key in Hex-Format
            hex_key = binascii.hexlify(key_bytes).decode('ascii').upper()
            self.key_display_var.set(hex_key)

            # Update Info
            crc = sum(key_bytes) % 65536
            self.key_info_display_var.set(f"32-Byte Key geladen (CRC: {crc:04X})")

        except Exception as e:
            messagebox.showerror("Fehler", f"Ungültiger Device-Key: {e}")

    def generate_new_key(self):
        """Generiert einen neuen Device-Key"""
        try:
            new_key = generate_device_key()
            self.set_device_key(new_key)

            with open("default_device_key.bin", "wb") as f:
                f.write(new_key)

            self.status_var.set("Neuer Device-Key generiert und gespeichert")

        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Generieren des Keys: {e}")

    def load_key_file(self):
        """Lädt Device-Key aus Datei"""
        try:
            filename = filedialog.askopenfilename(
                title="Device-Key laden",
                filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, "rb") as f:
                    key_data = f.read()

                self.set_device_key(key_data)
                self.status_var.set(f"Device-Key aus {os.path.basename(filename)} geladen")

        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Laden der Key-Datei: {e}")

    def save_key_file(self):
        """Speichert Device-Key in Datei"""
        try:
            if not self.device_key:
                messagebox.showwarning("Warnung", "Kein Device-Key zum Speichern vorhanden")
                return

            filename = filedialog.asksaveasfilename(
                title="Device-Key speichern",
                defaultextension=".bin",
                filetypes=[("Binary files", "*.bin"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, "wb") as f:
                    f.write(self.device_key)

                self.status_var.set(f"Device-Key in {os.path.basename(filename)} gespeichert")

        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Speichern der Key-Datei: {e}")

    def input_hex_key(self):
        """Dialog zum Eingeben eines Hex-Keys"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Device-Key von Hex eingeben")
        dialog.geometry("500x200")
        dialog.transient(self.root)
        dialog.grab_set()

        ttk.Label(dialog, text="Geben Sie den 32-Byte Key als Hex-String ein (64 Zeichen):").pack(pady=10)

        hex_var = tk.StringVar()
        hex_entry = ttk.Entry(dialog, textvariable=hex_var, width=70)
        hex_entry.pack(pady=5, padx=10, fill=tk.X)
        hex_entry.focus()

        def apply_hex_key():
            try:
                hex_string = hex_var.get().replace(" ", "").replace("-", "")
                if len(hex_string) != 64:
                    raise ValueError("Hex-String muss genau 64 Zeichen lang sein")

                key_bytes = binascii.unhexlify(hex_string)
                self.set_device_key(key_bytes)
                dialog.destroy()
                self.status_var.set("Device-Key von Hex-String gesetzt")

            except Exception as e:
                messagebox.showerror("Fehler", f"Ungültiger Hex-String: {e}")

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        ttk.Button(button_frame, text="Übernehmen", command=apply_hex_key).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Abbrechen", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

        hex_entry.bind('<Return>', lambda e: apply_hex_key())

    def apply_manual_key(self):
        """Wendet manuell eingegebenen Key an"""
        try:
            hex_string = self.manual_key_var.get().replace(" ", "").replace("-", "")
            if len(hex_string) != 64:
                raise ValueError("Hex-String muss genau 64 Zeichen lang sein")

            key_bytes = binascii.unhexlify(hex_string)
            self.set_device_key(key_bytes)
            self.manual_key_var.set("")  # Eingabe löschen
            self.status_var.set("Device-Key manuell gesetzt")

        except Exception as e:
            messagebox.showerror("Fehler", f"Ungültiger Hex-String: {e}")

    # Einzelne PIN-Generierung Funktionen
    def set_single_today(self):
        """Setzt das Kaufdatum auf heute"""
        today = datetime.now()
        self.single_day_var.set(str(today.day))
        self.single_month_var.set(str(today.month))
        self.single_year_var.set(str(today.year))

    def set_single_unlimited(self):
        """Setzt unbegrenzte Gültigkeit"""
        self.single_validity_var.set("255")
        self.single_day_var.set("31")
        self.single_month_var.set("12")
        self.single_year_var.set("2099")

    def generate_single_pin(self):
        """Generiert einen einzelnen PIN"""
        try:
            if not self.generator:
                messagebox.showerror("Fehler", "Kein Device-Key geladen")
                return

            day = int(self.single_day_var.get())
            month = int(self.single_month_var.get())
            year = int(self.single_year_var.get())
            purchase_date = datetime(year, month, day)

            validity_months = int(self.single_validity_var.get())

            if validity_months == 255 and year == 2099:
                pin = self.generator.generate_unlimited_pin()
                pin_type = "Unbegrenzt"
            else:
                pin = self.generator.generate_pin(purchase_date, validity_months)
                pin_type = "Zeitbasiert"

            self.single_pin_var.set(pin)

            # Info anzeigen
            self.single_info_text.delete(1.0, tk.END)
            self.single_info_text.insert(tk.END, f"PIN-Typ: {pin_type}\n")
            self.single_info_text.insert(tk.END, f"Kaufdatum: {purchase_date.strftime('%d.%m.%Y')}\n")
            self.single_info_text.insert(tk.END, f"Gültigkeitsmonate: {validity_months}\n")

            if pin_type == "Zeitbasiert":
                expiry_date = purchase_date + timedelta(days=validity_months * 30)
                self.single_info_text.insert(tk.END, f"Ablaufdatum: {expiry_date.strftime('%d.%m.%Y')}\n")
            else:
                self.single_info_text.insert(tk.END, "Ablaufdatum: Niemals\n")

            self.status_var.set(f"PIN generiert: {pin}")

        except ValueError as e:
            messagebox.showerror("Eingabefehler", f"Ungültige Eingabe: {e}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Generieren des PINs: {e}")

    def copy_single_pin(self):
        """Kopiert den einzelnen PIN in die Zwischenablage"""
        pin = self.single_pin_var.get()
        if pin:
            self.root.clipboard_clear()
            self.root.clipboard_append(pin)
            self.status_var.set("PIN in Zwischenablage kopiert")
        else:
            messagebox.showwarning("Warnung", "Kein PIN zum Kopieren vorhanden")

    # Batch-Generierung Funktionen
    def generate_batch_pins(self):
        """Generiert eine Batch von PINs"""
        try:
            if not self.generator:
                messagebox.showerror("Fehler", "Kein Device-Key geladen")
                return

            # Parameter lesen
            start_day = int(self.batch_start_day.get())
            start_month = int(self.batch_start_month.get())
            start_year = int(self.batch_start_year.get())
            start_date = datetime(start_year, start_month, start_day)

            count = int(self.batch_count_var.get())
            validity_months = int(self.batch_validity_var.get())
            interval_days = int(self.batch_interval_var.get())

            if count <= 0 or count > 1000:
                raise ValueError("Anzahl muss zwischen 1 und 1000 liegen")

            # Batch generieren
            self.batch_results = []
            current_date = start_date

            for i in range(count):
                pin = self.generator.generate_pin(current_date, validity_months)
                expiry_date = current_date + timedelta(days=validity_months * 30)

                result = {
                    'nr': i + 1,
                    'date': current_date,
                    'validity': validity_months,
                    'pin': pin,
                    'expiry': expiry_date
                }

                self.batch_results.append(result)
                current_date += timedelta(days=interval_days)

            # Ergebnisse in Treeview anzeigen
            self.update_batch_display()
            self.status_var.set(f"{count} PINs erfolgreich generiert")

        except ValueError as e:
            messagebox.showerror("Eingabefehler", f"Ungültige Eingabe: {e}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler bei der Batch-Generierung: {e}")

    def update_batch_display(self):
        """Aktualisiert die Batch-Anzeige"""
        # Alte Einträge löschen
        for item in self.batch_tree.get_children():
            self.batch_tree.delete(item)

        # Neue Einträge hinzufügen
        for result in self.batch_results:
            self.batch_tree.insert("", tk.END, values=(
                result['nr'],
                result['date'].strftime('%d.%m.%Y'),
                f"{result['validity']} Monate",
                result['pin'],
                result['expiry'].strftime('%d.%m.%Y')
            ))

    def export_batch_csv(self):
        """Exportiert Batch-Ergebnisse als CSV"""
        try:
            if not self.batch_results:
                messagebox.showwarning("Warnung", "Keine Batch-Ergebnisse zum Exportieren vorhanden")
                return

            filename = filedialog.asksaveasfilename(
                title="Batch-Ergebnisse exportieren",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile, delimiter=';')

                    # Header
                    writer.writerow(['Nr', 'Kaufdatum', 'Gültigkeit_Monate', 'PIN', 'Ablaufdatum'])

                    # Daten
                    for result in self.batch_results:
                        writer.writerow([
                            result['nr'],
                            result['date'].strftime('%d.%m.%Y'),
                            result['validity'],
                            result['pin'],
                            result['expiry'].strftime('%d.%m.%Y')
                        ])

                self.status_var.set(f"Batch-Ergebnisse in {os.path.basename(filename)} exportiert")

        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Exportieren: {e}")

    def clear_batch(self):
        """Löscht Batch-Ergebnisse"""
        self.batch_results = []
        self.update_batch_display()
        self.status_var.set("Batch-Ergebnisse gelöscht")

    # Validierung Funktionen
    def set_validation_today(self):
        """Setzt das Validierungsdatum auf heute"""
        today = datetime.now()
        self.val_day_var.set(str(today.day))
        self.val_month_var.set(str(today.month))
        self.val_year_var.set(str(today.year))

    def paste_validation_pin(self):
        """Fügt PIN aus Zwischenablage ein"""
        try:
            clipboard_content = self.root.clipboard_get()
            pin = ''.join(filter(str.isdigit, clipboard_content))
            if len(pin) == 8:
                self.val_pin_var.set(pin)
                self.status_var.set("PIN aus Zwischenablage eingefügt")
            else:
                messagebox.showwarning("Warnung", "Zwischenablage enthält keinen gültigen 8-stelligen PIN")
        except tk.TclError:
            messagebox.showwarning("Warnung", "Zwischenablage ist leer")

    def validate_pin(self):
        """Validiert den eingegebenen PIN"""
        try:
            if not self.validator:
                messagebox.showerror("Fehler", "Kein Device-Key geladen")
                return

            pin = self.val_pin_var.get().strip()
            if not pin:
                messagebox.showwarning("Warnung", "Bitte geben Sie einen PIN ein")
                return

            day = int(self.val_day_var.get())
            month = int(self.val_month_var.get())
            year = int(self.val_year_var.get())
            validation_date = datetime(year, month, day)

            result = self.validator.validate_pin(pin, validation_date)
            self.display_validation_result(result, validation_date)

        except ValueError as e:
            messagebox.showerror("Eingabefehler", f"Ungültige Eingabe: {e}")
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler bei der PIN-Validierung: {e}")

    def display_validation_result(self, result, validation_date):
        """Zeigt das Validierungsergebnis an"""
        self.validation_result_text.delete(1.0, tk.END)

        self.validation_result_text.insert(tk.END, f"PIN-Validierung vom {validation_date.strftime('%d.%m.%Y')}\n")
        self.validation_result_text.insert(tk.END, "=" * 60 + "\n\n")

        if result['valid']:
            if result['unlimited']:
                self.validation_result_text.insert(tk.END, "✅ GÜLTIG - UNBEGRENZTE LIZENZ\n\n")
                self.validation_result_text.insert(tk.END, f"Kaufdatum: {result['purchase_date'].strftime('%d.%m.%Y')}\n")
                self.validation_result_text.insert(tk.END, "Ablaufdatum: Niemals\n")
                self.validation_result_text.insert(tk.END, "Status: Unbegrenzt gültig\n\n")
                self.validation_result_text.insert(tk.END, "Diese Lizenz ist für interne Zwecke und läuft niemals ab.")
            else:
                self.validation_result_text.insert(tk.END, "✅ GÜLTIG - ZEITBASIERTE LIZENZ\n\n")
                self.validation_result_text.insert(tk.END, f"Kaufdatum: {result['purchase_date'].strftime('%d.%m.%Y')}\n")
                self.validation_result_text.insert(tk.END, f"Gültigkeitsdauer: {result['validity_months']} Monate\n")
                self.validation_result_text.insert(tk.END, f"Ablaufdatum: {result['expires'].strftime('%d.%m.%Y')}\n")
                self.validation_result_text.insert(tk.END, f"Verbleibende Tage: {result['days_remaining']}\n\n")

                if result['days_remaining'] <= 30:
                    self.validation_result_text.insert(tk.END, "⚠️  WARNUNG: Lizenz läuft in weniger als 30 Tagen ab!\n")
                    self.validation_result_text.insert(tk.END, "Bitte erneuern Sie die Lizenz rechtzeitig.\n")
                elif result['days_remaining'] <= 7:
                    self.validation_result_text.insert(tk.END, "🚨 KRITISCH: Lizenz läuft in weniger als 7 Tagen ab!\n")
                    self.validation_result_text.insert(tk.END, "Sofortige Erneuerung erforderlich!\n")
        else:
            self.validation_result_text.insert(tk.END, "❌ UNGÜLTIG\n\n")
            if 'error' in result:
                self.validation_result_text.insert(tk.END, f"Fehler: {result['error']}\n\n")
            else:
                self.validation_result_text.insert(tk.END, "Der PIN konnte nicht validiert werden oder ist abgelaufen.\n\n")

            self.validation_result_text.insert(tk.END, "Mögliche Ursachen:\n")
            self.validation_result_text.insert(tk.END, "• PIN ist abgelaufen\n")
            self.validation_result_text.insert(tk.END, "• PIN wurde für ein anderes Gerät generiert\n")
            self.validation_result_text.insert(tk.END, "• PIN wurde falsch eingegeben\n")
            self.validation_result_text.insert(tk.END, "• Falscher Device-Key verwendet\n")

        self.status_var.set(f"PIN-Validierung: {'GÜLTIG' if result['valid'] else 'UNGÜLTIG'}")


def main():
    """Hauptfunktion"""
    root = tk.Tk()
    app = AdvancedPinGUI(root)

    # Icon setzen (falls vorhanden)
    try:
        root.iconbitmap("icon.ico")
    except:
        pass

    root.mainloop()


if __name__ == "__main__":
    main()
