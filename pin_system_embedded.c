/*
 * Embedded PIN-System für Low-Power Mikrocontroller
 * Optimiert für minimalen RAM/Flash-Verbrauch und niedrigen Stromverbrauch
 */

#include <stdint.h>
#include <string.h>
#include <stdbool.h>

// Konfiguration für verschiedene MCU-Typen
#define DEVICE_KEY_SIZE 16      // Reduziert von 32 Bytes
#define PIN_LENGTH 8
#define MAX_SEARCH_ITERATIONS 400  // Begrenzt für Low-Power

// Datenstrukturen
typedef struct {
    uint16_t days_since_ref;    // Tage seit 1.1.2020
    uint8_t validity_months;    // 1-255 Monate
    uint8_t checksum;           // Einfache Prüfsumme
} __attribute__((packed)) license_data_t;

typedef struct {
    bool valid;
    bool unlimited;
    uint16_t days_since_ref;
    uint8_t validity_months;
    int16_t days_remaining;
} pin_result_t;

// Globaler Device-Key (sollte in sicherem Flash/EEPROM gespeichert werden)
static uint8_t device_key[DEVICE_KEY_SIZE];

/*
 * Einfache aber sichere Hash-Funktion für Embedded Systeme
 * Verwendet nur grundlegende 32-Bit Operationen
 */
static uint32_t simple_hash(const uint8_t* data, uint8_t len) {
    uint32_t seed = *(uint32_t*)device_key;  // Ersten 4 Bytes als Seed
    uint32_t hash = seed;
    
    for (uint8_t i = 0; i < len; i++) {
        // Einfache aber effektive Mixing-Operationen
        hash ^= data[i] << (i % 24);
        hash = ((hash << 13) | (hash >> 19));  // Rotate left 13
        hash ^= *(uint32_t*)(device_key + 4 + (i % 3) * 4);
    }
    
    return hash;
}

/*
 * Berechnet Prüfsumme für Lizenz-Daten
 */
static uint8_t calculate_checksum(const license_data_t* data) {
    uint8_t sum = data->days_since_ref & 0xFF;
    sum += (data->days_since_ref >> 8) & 0xFF;
    sum += data->validity_months;
    
    // Addiere ersten 4 Bytes des Device-Keys
    for (int i = 0; i < 4; i++) {
        sum += device_key[i];
    }
    
    return sum;
}

/*
 * Generiert PIN aus Lizenz-Daten
 */
uint32_t generate_pin(uint16_t days_since_ref, uint8_t validity_months) {
    license_data_t license;
    license.days_since_ref = days_since_ref;
    license.validity_months = validity_months;
    license.checksum = calculate_checksum(&license);
    
    // Hash berechnen
    uint32_t hash = simple_hash((uint8_t*)&license, sizeof(license));
    
    // PIN aus Hash ableiten (8 Stellen)
    return hash % 100000000UL;
}

/*
 * Testet eine spezifische Kombination von Parametern
 */
static bool test_combination(uint32_t target_pin, uint16_t days, uint8_t months) {
    uint32_t calculated_pin = generate_pin(days, months);
    return calculated_pin == target_pin;
}

/*
 * Konvertiert PIN-String zu Integer
 */
static uint32_t pin_string_to_int(const char* pin_str) {
    uint32_t result = 0;
    
    // Validiere Format
    for (int i = 0; i < PIN_LENGTH; i++) {
        if (pin_str[i] < '0' || pin_str[i] > '9') {
            return 0;  // Ungültiger PIN
        }
        result = result * 10 + (pin_str[i] - '0');
    }
    
    return result;
}

/*
 * Hauptfunktion zur PIN-Validierung
 * Optimiert für minimale CPU-Zyklen und Stromverbrauch
 */
pin_result_t validate_pin(const char* pin_str, uint16_t current_days_since_ref) {
    pin_result_t result = {false, false, 0, 0, 0};
    
    // PIN-Format validieren
    if (strlen(pin_str) != PIN_LENGTH) {
        return result;
    }
    
    uint32_t pin_int = pin_string_to_int(pin_str);
    if (pin_int == 0 && pin_str[0] != '0') {
        return result;  // Ungültiger PIN
    }
    
    uint16_t search_count = 0;
    
    // 1. Teste unbegrenzte Lizenz zuerst (1 Iteration)
    uint16_t unlimited_days = 29219;  // 31.12.2099 seit 1.1.2020
    if (test_combination(pin_int, unlimited_days, 255)) {
        result.valid = true;
        result.unlimited = true;
        result.days_since_ref = unlimited_days;
        result.validity_months = 255;
        result.days_remaining = 32767;  // "Unendlich"
        return result;
    }
    search_count++;
    
    // 2. Intelligente Suche um aktuelles Datum
    // Teste ±3 Jahre mit häufigen Gültigkeiten
    uint16_t start_days = (current_days_since_ref > 1095) ? 
                         current_days_since_ref - 1095 : 0;
    uint16_t end_days = current_days_since_ref + 1095;
    if (end_days > 65535) end_days = 65535;
    
    // Häufige Gültigkeitswerte (sortiert nach Wahrscheinlichkeit)
    uint8_t common_months[] = {12, 6, 24, 1, 3, 18, 36, 48, 60};
    uint8_t num_common = sizeof(common_months) / sizeof(common_months[0]);
    
    // Teste jeden 30. Tag (monatlich) mit häufigen Werten
    for (uint16_t days = start_days; days <= end_days && search_count < MAX_SEARCH_ITERATIONS; days += 30) {
        for (uint8_t i = 0; i < num_common && search_count < MAX_SEARCH_ITERATIONS; i++) {
            if (test_combination(pin_int, days, common_months[i])) {
                result.valid = true;
                result.unlimited = false;
                result.days_since_ref = days;
                result.validity_months = common_months[i];
                
                // Berechne verbleibende Tage
                uint16_t expiry_days = days + (common_months[i] * 30);
                result.days_remaining = (int16_t)expiry_days - (int16_t)current_days_since_ref;
                
                // Prüfe ob noch gültig
                result.valid = (result.days_remaining >= 0);
                
                return result;
            }
            search_count++;
        }
    }
    
    // 3. Erweiterte Suche für seltene Fälle (nur wenn nötig)
    // Teste spezifische wahrscheinliche Daten mit allen Gültigkeiten
    uint16_t likely_dates[] = {
        current_days_since_ref,      // Heute
        current_days_since_ref - 365, // Vor 1 Jahr
        current_days_since_ref - 730, // Vor 2 Jahren
    };
    uint8_t num_likely = sizeof(likely_dates) / sizeof(likely_dates[0]);
    
    for (uint8_t d = 0; d < num_likely && search_count < MAX_SEARCH_ITERATIONS; d++) {
        uint16_t days = likely_dates[d];
        if (days > 65535) continue;
        
        for (uint16_t months = 1; months <= 255 && search_count < MAX_SEARCH_ITERATIONS; months++) {
            if (test_combination(pin_int, days, (uint8_t)months)) {
                result.valid = true;
                result.unlimited = false;
                result.days_since_ref = days;
                result.validity_months = (uint8_t)months;
                
                uint16_t expiry_days = days + (months * 30);
                result.days_remaining = (int16_t)expiry_days - (int16_t)current_days_since_ref;
                result.valid = (result.days_remaining >= 0);
                
                return result;
            }
            search_count++;
        }
    }
    
    return result;  // PIN nicht gefunden
}

/*
 * Initialisiert das PIN-System mit Device-Key
 */
void pin_system_init(const uint8_t* key) {
    memcpy(device_key, key, DEVICE_KEY_SIZE);
}

/*
 * Hilfsfunktion: Berechnet Tage seit 1.1.2020
 */
uint16_t days_since_2020(uint16_t year, uint8_t month, uint8_t day) {
    // Vereinfachte Berechnung (ohne Schaltjahre für Embedded)
    uint16_t days = 0;
    
    // Jahre seit 2020
    days += (year - 2020) * 365;
    
    // Monate im aktuellen Jahr
    uint8_t days_per_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    for (uint8_t m = 1; m < month; m++) {
        days += days_per_month[m - 1];
    }
    
    // Tage im aktuellen Monat
    days += day - 1;
    
    return days;
}

/*
 * Beispiel-Verwendung und Test
 */
#ifdef EMBEDDED_PIN_TEST
#include <stdio.h>

int main() {
    // Test Device-Key
    uint8_t test_key[DEVICE_KEY_SIZE] = {
        0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
        0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10
    };
    
    pin_system_init(test_key);
    
    // Generiere Test-PIN für 12 Monate ab heute
    uint16_t today = days_since_2020(2024, 8, 19);
    uint32_t test_pin = generate_pin(today, 12);
    
    printf("Generierter PIN: %08lu\n", test_pin);
    
    // Validiere PIN
    char pin_str[9];
    sprintf(pin_str, "%08lu", test_pin);
    
    pin_result_t result = validate_pin(pin_str, today);
    
    printf("Validierung: %s\n", result.valid ? "GÜLTIG" : "UNGÜLTIG");
    if (result.valid) {
        printf("Unbegrenzt: %s\n", result.unlimited ? "JA" : "NEIN");
        printf("Gültigkeitsmonate: %d\n", result.validity_months);
        printf("Verbleibende Tage: %d\n", result.days_remaining);
    }
    
    return 0;
}
#endif

/*
 * Ressourcen-Verbrauch (geschätzt für ARM Cortex-M0):
 * - Flash: ~2KB Code
 * - RAM: ~50 Bytes statisch + ~20 Bytes Stack
 * - CPU-Zyklen: 1000-5000 pro Validierung (typ. 2000)
 * - Stromverbrauch: ~0.1-0.5mA für 1-5ms bei 1MHz
 */
